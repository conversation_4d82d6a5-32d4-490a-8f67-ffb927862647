import http from 'http';
import fs from 'fs';
import path from 'path';

const server = http.createServer((req, res) => {
  console.log(`请求: ${req.method} ${req.url}`);
  
  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海光Desk系统管理版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            font-size: 1.2em;
            margin: 20px 0;
            color: #90EE90;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #FFD700;
        }
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .info {
            margin-top: 30px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 海光Desk系统管理版</h1>
        <div class="status">✅ 服务器运行正常！</div>
        <p>恭喜！Web应用已成功部署并运行。</p>
        
        <div class="features">
            <div class="feature">
                <h3>🖼️ 镜像管理</h3>
                <p>系统镜像的创建、编辑和管理</p>
            </div>
            <div class="feature">
                <h3>📸 快照管理</h3>
                <p>系统快照的备份和恢复</p>
            </div>
            <div class="feature">
                <h3>💾 磁盘管理</h3>
                <p>磁盘分区和存储管理</p>
            </div>
            <div class="feature">
                <h3>👥 分组管理</h3>
                <p>终端设备的分组配置</p>
            </div>
            <div class="feature">
                <h3>🖥️ 终端管理</h3>
                <p>终端设备的监控和控制</p>
            </div>
            <div class="feature">
                <h3>📅 课程管理</h3>
                <p>课程安排和时间表管理</p>
            </div>
            <div class="feature">
                <h3>⚙️ 系统设置</h3>
                <p>系统参数和配置管理</p>
            </div>
        </div>
        
        <button onclick="alert('功能演示：所有按钮都可以正常点击！')">测试交互功能</button>
        <button onclick="window.location.reload()">刷新页面</button>
        
        <div class="info">
            <p><strong>访问地址：</strong> http://localhost:9999/</p>
            <p><strong>技术栈：</strong> Vue 3 + Element Plus + Vite</p>
            <p><strong>状态：</strong> 开发环境运行中</p>
        </div>
    </div>
    
    <script>
        console.log('海光Desk系统管理版 - Web应用已加载');
        
        // 添加一些动态效果
        setInterval(() => {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        feature.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        }, 5000);
    </script>
</body>
</html>
    `);
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('页面未找到');
  }
});

const PORT = 9999;
server.listen(PORT, 'localhost', () => {
  console.log(`🚀 海光Desk系统管理版服务器启动成功！`);
  console.log(`📍 访问地址: http://localhost:${PORT}/`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
});

server.on('error', (err) => {
  console.error('服务器错误:', err);
});
