<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>海光Desk系统管理版</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/image-management">
            <el-icon><Picture /></el-icon>
            <span>镜像管理</span>
          </el-menu-item>
          <el-menu-item index="/snapshot-management">
            <el-icon><Camera /></el-icon>
            <span>系统快照管理</span>
          </el-menu-item>
          <el-menu-item index="/disk-management">
            <el-icon><Coin /></el-icon>
            <span>磁盘管理</span>
          </el-menu-item>
          <el-menu-item index="/group-management">
            <el-icon><UserFilled /></el-icon>
            <span>分组管理</span>
          </el-menu-item>
          <el-menu-item index="/terminal-management">
            <el-icon><Monitor /></el-icon>
            <span>终端管理</span>
          </el-menu-item>
          <el-menu-item index="/course-management">
            <el-icon><Calendar /></el-icon>
            <span>课程管理</span>
          </el-menu-item>
          <el-menu-item index="/system-settings">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <el-header class="header">
          <div class="header-content">
            <h3>{{ getPageTitle() }}</h3>
            <div class="header-actions">
              <el-button type="primary" size="small">
                <el-icon><User /></el-icon>
                管理员
              </el-button>
            </div>
          </div>
        </el-header>
        
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'

const route = useRoute()

const getPageTitle = () => {
  const titleMap = {
    '/image-management': '镜像管理',
    '/snapshot-management': '系统快照管理',
    '/disk-management': '磁盘管理',
    '/group-management': '分组管理',
    '/terminal-management': '终端管理',
    '/course-management': '课程管理',
    '/system-settings': '系统设置'
  }
  return titleMap[route.path] || '海光Desk系统管理版'
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  margin-bottom: 0;
}

.logo h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h3 {
  margin: 0;
  color: #303133;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}
</style>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
</style>
