import Mock from 'mockjs'

// 设置Mock拦截Ajax请求
Mock.setup({
  timeout: '200-600'
})

// 镜像管理Mock数据
const images = Mock.mock({
  'list|10-20': [{
    'id|+1': 1,
    'name': '@word(5,10)_镜像',
    'type|1': ['系统分区', '数据分区'],
    'size|100-5000': 1024,
    'status|1': ['上传中', '已完成'],
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'description': '@sentence(10,20)'
  }]
}).list

// 系统快照Mock数据
const snapshots = Mock.mock({
  'list|8-15': [{
    'id|+1': 1,
    'name': '@word(5,10)_快照',
    'type|1': ['完整快照', '增量快照'],
    'systemImage': '@word(5,8)_系统镜像',
    'dataImage': '@word(5,8)_数据镜像',
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")',
    'description': '@sentence(10,20)',
    'partitions|2-4': [{
      'name': '@word(3,5)盘 - @word(2,4)分区'
    }]
  }]
}).list

// 磁盘目录树Mock数据
const diskTree = [
  {
    id: 1,
    name: '教学磁盘',
    type: 'directory',
    children: [
      { id: 11, name: '计算机基础', type: 'directory' },
      { id: 12, name: '程序设计', type: 'directory' },
      { id: 13, name: 'Win10_教学盘', type: 'disk' }
    ]
  },
  {
    id: 2,
    name: '办公磁盘',
    type: 'directory',
    children: [
      { id: 21, name: 'Office_2019', type: 'disk' },
      { id: 22, name: 'WPS_办公', type: 'disk' }
    ]
  }
]

// 磁盘Mock数据
const disks = Mock.mock({
  'list|5-10': [{
    'id|+1': 1,
    'name': '@word(5,10)_磁盘',
    'size|50-500': 100,
    'showSystemMenu|1': [true, false],
    'showHelp|1': [true, false],
    'delayShutdown|1': [true, false],
    'delayTime|10-60': 30
  }]
}).list

// 操作系统Mock数据
const operatingSystems = Mock.mock({
  'list|3-8': [{
    'id|+1': 1,
    'name': '@word(5,10)_系统',
    'snapshotName': '@word(5,8)_快照',
    'showSystem|1': [true, false],
    'supportShare|1': [true, false],
    'isDefault|1': [true, false],
    'waitTime|5-30': 10,
    'rootImage': '@word(5,8)_根分区镜像'
  }]
}).list

// 分组目录树Mock数据
const groupTree = [
  {
    id: 1,
    name: '教学机房',
    type: 'directory',
    children: [
      { id: 11, name: '机房1', type: 'directory' },
      { id: 12, name: '机房2', type: 'directory' },
      { id: 13, name: '机房3', type: 'directory' }
    ]
  },
  {
    id: 2,
    name: '办公区域',
    type: 'directory',
    children: [
      { id: 21, name: '办公室1', type: 'directory' },
      { id: 22, name: '会议室', type: 'directory' }
    ]
  }
]

// 分组Mock数据
const groups = Mock.mock({
  'list|4-8': [{
    'id|+1': 1,
    'name': '@word(3,6)分组',
    'terminalCount|20-50': 30,
    'onlineCount|10-30': 20,
    'deployStatus|1': ['部署中', '已完成', '未部署', '部署失败'],
    'diskInfo': '@word(5,8)磁盘',
    'ipRange': '192.168.@integer(1,10).100-150',
    'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
  }]
}).list

// 终端Mock数据
const terminals = Mock.mock({
  'list|15-30': [{
    'id|+1': 1,
    'hostNumber|1-254': 100,
    'name': 'PC@integer(1,100)',
    'ipAddress': '192.168.1.@integer(100,200)',
    'macAddress': '@word(2,2):@word(2,2):@word(2,2):@word(2,2):@word(2,2):@word(2,2)',
    'onlineStatus|1': ['在线', '离线'],
    'terminalStatus|1': ['运行中', '部署中', '离线', '待机'],
    'deployMode|1': ['智能部署', '强制部署', '按需部署'],
    'undeployedData|0-1000': 500,
    'instantSpeed|0-100': 50,
    'averageSpeed|0-80': 40,
    'remainingTime|0-60': 30,
    'presetCommand|1': ['无', '更新客户端', '重新部署', '完全重新部署'],
    'subGroupNumber|1-5': 1,
    'controlIP': '192.168.1.@integer(1,10)',
    'networkSpeed': '@integer(10,1000)Mbps',
    'usedSnapshot': '@word(5,8)_快照'
  }]
}).list

// 课程Mock数据
const courses = Mock.mock({
  'list|10-20': [{
    'id|+1': 1,
    'name': '@word(3,6)课程',
    'dayOfWeek|1': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    'classroom': '@word(3,6)教室',
    'timeSlot': '@pick(["08:00-09:00", "09:10-10:10", "10:20-11:20", "11:30-12:30", "14:00-15:00", "15:10-16:10", "16:20-17:20", "17:30-18:30"])',
    'timeSlotId|1-8': 1,
    'system': '@pick(["Win10_Office_2019", "Win7_Dev_Tools", "Ubuntu_20_04_Server"])',
    'stayTime|5-30': 10,
    'courseDescription': '@sentence(10,20)',
    'classroomDescription': '@sentence(8,15)',
    'deployToLocal|1': [true, false],
    'status|1': ['scheduled', 'ongoing', 'selected']
  }]
}).list

// 系统设置Mock数据
const systemSettings = {
  server: {
    serverIP: '*************',
    serverPort: 8080,
    databaseUrl: '*****************************************',
    maxConnections: 100,
    connectionTimeout: 30
  },
  runtime: {
    realtimeCacheSize: 200,
    deployCacheSize: 200,
    autoSelectGroupDeploy: true,
    maxConcurrentDeploy: 10,
    deployTimeout: 300
  },
  storage: {
    imagePath: 'D:\\HaiguangDesk\\Images',
    snapshotPath: 'D:\\HaiguangDesk\\Snapshots',
    tempPath: 'D:\\HaiguangDesk\\Temp',
    logPath: 'D:\\HaiguangDesk\\Logs',
    autoCleanTemp: true,
    cleanInterval: 7,
    maxLogSize: 100
  }
}

// Mock API接口
Mock.mock('/api/images', 'get', () => {
  return {
    code: 200,
    data: images,
    message: 'success'
  }
})

Mock.mock('/api/images', 'post', (options) => {
  const body = JSON.parse(options.body)
  const newImage = {
    id: images.length + 1,
    ...body,
    size: Mock.Random.integer(100, 5000),
    status: '上传中',
    createTime: new Date().toLocaleString()
  }
  images.push(newImage)
  return {
    code: 200,
    data: newImage,
    message: 'success'
  }
})

Mock.mock(/\/api\/images\/\d+/, 'put', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: body,
    message: 'success'
  }
})

Mock.mock(/\/api\/images\/\d+/, 'delete', () => {
  return {
    code: 200,
    message: 'success'
  }
})

Mock.mock('/api/snapshots', 'get', () => {
  return {
    code: 200,
    data: snapshots,
    message: 'success'
  }
})

Mock.mock('/api/snapshots', 'post', (options) => {
  const body = JSON.parse(options.body)
  const newSnapshot = {
    id: snapshots.length + 1,
    ...body,
    createTime: new Date().toLocaleString()
  }
  snapshots.push(newSnapshot)
  return {
    code: 200,
    data: newSnapshot,
    message: 'success'
  }
})

Mock.mock(/\/api\/snapshots\/\d+/, 'put', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: body,
    message: 'success'
  }
})

Mock.mock(/\/api\/snapshots\/\d+/, 'delete', () => {
  return {
    code: 200,
    message: 'success'
  }
})

Mock.mock(/\/api\/snapshots\/\d+\/copy/, 'post', (options) => {
  const body = JSON.parse(options.body)
  const newSnapshot = {
    id: snapshots.length + 1,
    ...body,
    createTime: new Date().toLocaleString()
  }
  snapshots.push(newSnapshot)
  return {
    code: 200,
    data: newSnapshot,
    message: 'success'
  }
})

Mock.mock('/api/snapshots/merge', 'post', (options) => {
  const body = JSON.parse(options.body)
  const mergedSnapshot = {
    id: snapshots.length + 1,
    name: body.name,
    type: '合并快照',
    systemImage: '合并镜像',
    dataImage: '合并镜像',
    createTime: new Date().toLocaleString(),
    description: body.description
  }
  snapshots.push(mergedSnapshot)
  return {
    code: 200,
    data: mergedSnapshot,
    message: 'success'
  }
})

Mock.mock('/api/disk-tree', 'get', () => {
  return {
    code: 200,
    data: diskTree,
    message: 'success'
  }
})

Mock.mock(/\/api\/disks/, 'get', () => {
  return {
    code: 200,
    data: disks,
    message: 'success'
  }
})

Mock.mock('/api/disks', 'post', (options) => {
  const body = JSON.parse(options.body)
  const newDisk = {
    id: disks.length + 1,
    ...body
  }
  disks.push(newDisk)
  return {
    code: 200,
    data: newDisk,
    message: 'success'
  }
})

Mock.mock(/\/api\/operating-systems/, 'get', () => {
  return {
    code: 200,
    data: operatingSystems,
    message: 'success'
  }
})

Mock.mock(/\/api\/disks\/\d+\/operating-systems/, 'post', (options) => {
  const body = JSON.parse(options.body)
  const newSystem = {
    id: operatingSystems.length + 1,
    ...body
  }
  operatingSystems.push(newSystem)
  return {
    code: 200,
    data: newSystem,
    message: 'success'
  }
})

Mock.mock(/\/api\/disks\/\d+\/partitions/, 'post', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: { id: Date.now(), ...body },
    message: 'success'
  }
})

Mock.mock('/api/group-tree', 'get', () => {
  return {
    code: 200,
    data: groupTree,
    message: 'success'
  }
})

Mock.mock(/\/api\/groups/, 'get', () => {
  return {
    code: 200,
    data: groups,
    message: 'success'
  }
})

Mock.mock('/api/groups', 'post', (options) => {
  const body = JSON.parse(options.body)
  const newGroup = {
    id: groups.length + 1,
    ...body,
    terminalCount: 0,
    onlineCount: 0,
    deployStatus: '未部署',
    createTime: new Date().toLocaleString()
  }
  groups.push(newGroup)
  return {
    code: 200,
    data: newGroup,
    message: 'success'
  }
})

Mock.mock(/\/api\/terminals/, 'get', () => {
  return {
    code: 200,
    data: terminals,
    message: 'success'
  }
})

Mock.mock(/\/api\/terminals\/\d+/, 'put', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: body,
    message: 'success'
  }
})

Mock.mock('/api/courses', 'get', () => {
  return {
    code: 200,
    data: courses,
    message: 'success'
  }
})

Mock.mock('/api/courses', 'post', (options) => {
  const body = JSON.parse(options.body)
  const newCourse = {
    id: courses.length + 1,
    ...body,
    timeSlotId: Mock.Random.integer(1, 8),
    status: 'scheduled'
  }
  courses.push(newCourse)
  return {
    code: 200,
    data: newCourse,
    message: 'success'
  }
})

Mock.mock(/\/api\/courses\/\d+/, 'put', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: body,
    message: 'success'
  }
})

Mock.mock(/\/api\/courses\/\d+/, 'delete', () => {
  return {
    code: 200,
    message: 'success'
  }
})

Mock.mock('/api/system-settings', 'get', () => {
  return {
    code: 200,
    data: systemSettings,
    message: 'success'
  }
})

Mock.mock(/\/api\/system-settings\/\w+/, 'put', (options) => {
  const body = JSON.parse(options.body)
  return {
    code: 200,
    data: body,
    message: 'success'
  }
})

console.log('Mock数据服务已启动')
