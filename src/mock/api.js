import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    if (data.code === 200) {
      return data.data
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('API请求错误:', error)
    throw error
  }
)

// 镜像管理API
export const getImages = () => api.get('/images')
export const createImage = (data) => api.post('/images', data)
export const updateImage = (id, data) => api.put(`/images/${id}`, data)
export const deleteImage = (id) => api.delete(`/images/${id}`)

// 系统快照管理API
export const getSnapshots = () => api.get('/snapshots')
export const createSnapshot = (data) => api.post('/snapshots', data)
export const updateSnapshot = (id, data) => api.put(`/snapshots/${id}`, data)
export const deleteSnapshot = (id) => api.delete(`/snapshots/${id}`)
export const copySnapshot = (id, data) => api.post(`/snapshots/${id}/copy`, data)
export const mergeSnapshots = (data) => api.post('/snapshots/merge', data)

// 磁盘管理API
export const getDiskTree = () => api.get('/disk-tree')
export const getDisks = (directoryId) => api.get(`/disks/${directoryId || ''}`)
export const createDisk = (data) => api.post('/disks', data)
export const updateDisk = (id, data) => api.put(`/disks/${id}`, data)
export const deleteDisk = (id) => api.delete(`/disks/${id}`)

export const getOperatingSystems = (diskId) => api.get(`/operating-systems/${diskId || ''}`)
export const createOperatingSystem = (diskId, data) => api.post(`/disks/${diskId}/operating-systems`, data)
export const updateOperatingSystem = (id, data) => api.put(`/operating-systems/${id}`, data)
export const deleteOperatingSystem = (id) => api.delete(`/operating-systems/${id}`)

export const createPartition = (diskId, data) => api.post(`/disks/${diskId}/partitions`, data)

// 分组管理API
export const getGroupTree = () => api.get('/group-tree')
export const getGroups = (directoryId) => api.get(`/groups/${directoryId || ''}`)
export const createGroup = (data) => api.post('/groups', data)
export const updateGroup = (id, data) => api.put(`/groups/${id}`, data)
export const deleteGroup = (id) => api.delete(`/groups/${id}`)

// 终端管理API
export const getTerminals = (groupId) => api.get(`/terminals/${groupId || ''}`)
export const updateTerminal = (id, data) => api.put(`/terminals/${id}`, data)
export const deleteTerminal = (id) => api.delete(`/terminals/${id}`)

// 课程管理API
export const getCourses = () => api.get('/courses')
export const createCourse = (data) => api.post('/courses', data)
export const updateCourse = (id, data) => api.put(`/courses/${id}`, data)
export const deleteCourse = (id) => api.delete(`/courses/${id}`)

// 系统设置API
export const getSystemSettings = () => api.get('/system-settings')
export const updateSystemSettings = (type, data) => api.put(`/system-settings/${type}`, data)

export default api
