import { defineStore } from 'pinia'

// 镜像管理状态
export const useImageStore = defineStore('image', {
  state: () => ({
    images: [],
    loading: false
  }),
  actions: {
    setImages(images) {
      this.images = images
    },
    setLoading(loading) {
      this.loading = loading
    },
    addImage(image) {
      this.images.push(image)
    },
    updateImage(id, updatedImage) {
      const index = this.images.findIndex(img => img.id === id)
      if (index !== -1) {
        this.images[index] = { ...this.images[index], ...updatedImage }
      }
    },
    deleteImage(id) {
      this.images = this.images.filter(img => img.id !== id)
    }
  }
})

// 系统快照管理状态
export const useSnapshotStore = defineStore('snapshot', {
  state: () => ({
    snapshots: [],
    loading: false
  }),
  actions: {
    setSnapshots(snapshots) {
      this.snapshots = snapshots
    },
    setLoading(loading) {
      this.loading = loading
    },
    addSnapshot(snapshot) {
      this.snapshots.push(snapshot)
    },
    updateSnapshot(id, updatedSnapshot) {
      const index = this.snapshots.findIndex(snap => snap.id === id)
      if (index !== -1) {
        this.snapshots[index] = { ...this.snapshots[index], ...updatedSnapshot }
      }
    },
    deleteSnapshot(id) {
      this.snapshots = this.snapshots.filter(snap => snap.id !== id)
    }
  }
})

// 磁盘管理状态
export const useDiskStore = defineStore('disk', {
  state: () => ({
    diskTree: [],
    disks: [],
    operatingSystems: [],
    loading: false
  }),
  actions: {
    setDiskTree(tree) {
      this.diskTree = tree
    },
    setDisks(disks) {
      this.disks = disks
    },
    setOperatingSystems(systems) {
      this.operatingSystems = systems
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
})

// 分组管理状态
export const useGroupStore = defineStore('group', {
  state: () => ({
    groupTree: [],
    groups: [],
    loading: false
  }),
  actions: {
    setGroupTree(tree) {
      this.groupTree = tree
    },
    setGroups(groups) {
      this.groups = groups
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
})

// 终端管理状态
export const useTerminalStore = defineStore('terminal', {
  state: () => ({
    terminals: [],
    loading: false
  }),
  actions: {
    setTerminals(terminals) {
      this.terminals = terminals
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
})

// 课程管理状态
export const useCourseStore = defineStore('course', {
  state: () => ({
    courses: [],
    currentWeek: 1,
    currentYear: new Date().getFullYear(),
    viewMode: 'table', // 'table' or 'list'
    loading: false
  }),
  actions: {
    setCourses(courses) {
      this.courses = courses
    },
    setCurrentWeek(week) {
      this.currentWeek = week
    },
    setViewMode(mode) {
      this.viewMode = mode
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
})

// 系统设置状态
export const useSystemStore = defineStore('system', {
  state: () => ({
    settings: {},
    loading: false
  }),
  actions: {
    setSettings(settings) {
      this.settings = settings
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
})
