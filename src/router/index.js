import { createRouter, createWebHistory } from 'vue-router'
import ImageManagement from '../views/ImageManagement.vue'
import SnapshotManagement from '../views/SnapshotManagement.vue'
import DiskManagement from '../views/DiskManagement.vue'
import GroupManagement from '../views/GroupManagement.vue'
import TerminalManagement from '../views/TerminalManagement.vue'
import CourseManagement from '../views/CourseManagement.vue'
import SystemSettings from '../views/SystemSettings.vue'

const routes = [
  // 暂时简化路由，只保留根路径
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
