import { createRouter, createWebHistory } from 'vue-router'
import ImageManagement from '../views/ImageManagement.vue'
import SnapshotManagement from '../views/SnapshotManagement.vue'
import DiskManagement from '../views/DiskManagement.vue'
import GroupManagement from '../views/GroupManagement.vue'
import TerminalManagement from '../views/TerminalManagement.vue'
import CourseManagement from '../views/CourseManagement.vue'
import SystemSettings from '../views/SystemSettings.vue'

const routes = [
  {
    path: '/',
    redirect: '/image-management'
  },
  {
    path: '/image-management',
    name: 'ImageManagement',
    component: ImageManagement
  },
  {
    path: '/snapshot-management',
    name: 'SnapshotManagement',
    component: SnapshotManagement
  },
  {
    path: '/disk-management',
    name: 'DiskManagement',
    component: DiskManagement
  },
  {
    path: '/group-management',
    name: 'GroupManagement',
    component: GroupManagement
  },
  {
    path: '/terminal-management',
    name: 'TerminalManagement',
    component: TerminalManagement
  },
  {
    path: '/course-management',
    name: 'CourseManagement',
    component: CourseManagement
  },
  {
    path: '/system-settings',
    name: 'SystemSettings',
    component: SystemSettings
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
