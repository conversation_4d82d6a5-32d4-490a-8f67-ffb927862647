<template>
  <div class="snapshot-management">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleCreateSnapshot">
          <el-icon><Plus /></el-icon>
          新建系统快照
        </el-button>
        <el-button type="success" @click="handleMergeSnapshot">
          <el-icon><Connection /></el-icon>
          合并系统快照
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="handleEditColumns">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchText"
          placeholder="请输入快照名称搜索"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="filteredSnapshots"
      v-loading="loading"
      stripe
      style="width: 100%"
      class="data-table"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="name" label="系统快照名称" min-width="200" />
      <el-table-column prop="type" label="系统快照类型" width="120" />
      <el-table-column prop="systemImage" label="系统分区镜像" min-width="150" />
      <el-table-column prop="dataImage" label="数据分区镜像" min-width="150" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEditProperties(row)">
            属性
          </el-button>
          <el-button size="small" type="primary" @click="handleCopy(row)">
            复制
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建快照对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建系统快照"
      width="600px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="快照名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入系统快照名称" />
        </el-form-item>
        <el-form-item label="系统分区镜像" prop="systemImage">
          <el-select v-model="createForm.systemImage" placeholder="请选择系统分区镜像">
            <el-option label="Win10_64_C" value="Win10_64_C" />
            <el-option label="Win7_64_C" value="Win7_64_C" />
            <el-option label="Ubuntu_20_04" value="Ubuntu_20_04" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据分区镜像" prop="dataImage">
          <el-select v-model="createForm.dataImage" placeholder="请选择数据分区镜像">
            <el-option label="Office_2019_D" value="Office_2019_D" />
            <el-option label="Dev_Tools_D" value="Dev_Tools_D" />
            <el-option label="Common_Software_D" value="Common_Software_D" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入快照描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 复制快照对话框 -->
    <el-dialog
      v-model="copyDialogVisible"
      title="复制系统快照"
      width="500px"
      @close="resetCopyForm"
    >
      <el-form :model="copyForm" :rules="copyRules" ref="copyFormRef" label-width="120px">
        <el-form-item label="原快照名称">
          <el-input :value="copyForm.originalName" disabled />
        </el-form-item>
        <el-form-item label="新快照名称" prop="name">
          <el-input v-model="copyForm.name" placeholder="请输入新的快照名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="copyForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入快照描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="copyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCopyConfirm">确认</el-button>
      </template>
    </el-dialog>

    <!-- 合并快照对话框 -->
    <el-dialog
      v-model="mergeDialogVisible"
      title="合并系统快照"
      width="600px"
      @close="resetMergeForm"
    >
      <el-form :model="mergeForm" :rules="mergeRules" ref="mergeFormRef" label-width="120px">
        <el-form-item label="合并后名称" prop="name">
          <el-input v-model="mergeForm.name" placeholder="请输入合并后的快照名称" />
        </el-form-item>
        <el-form-item label="选择快照" prop="selectedSnapshots">
          <div class="snapshot-list">
            <el-checkbox-group v-model="mergeForm.selectedSnapshots">
              <div v-for="snapshot in availableSnapshots" :key="snapshot.id" class="snapshot-item">
                <el-checkbox :label="snapshot.id">
                  {{ snapshot.name }} ({{ snapshot.type }})
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          <div class="merge-tip">
            <el-text type="info" size="small">请选择至少2个快照进行合并</el-text>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="mergeForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入合并后的快照描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="mergeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleMergeConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑属性对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑快照属性"
      width="600px"
      @close="resetEditForm"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-form-item label="快照名称" prop="name">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="数据分区" v-if="editForm.partitions && editForm.partitions.length > 0">
          <div class="partition-list">
            <div
              v-for="(partition, index) in editForm.partitions"
              :key="index"
              class="partition-item"
            >
              <span>{{ partition.name }}</span>
              <div class="partition-actions">
                <el-button size="small" @click="movePartitionUp(index)" :disabled="index === 0">
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button size="small" @click="movePartitionDown(index)" :disabled="index === editForm.partitions.length - 1">
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSnapshotStore } from '../store'
import { getSnapshots, createSnapshot, updateSnapshot, deleteSnapshot, copySnapshot, mergeSnapshots } from '../mock/api'

const snapshotStore = useSnapshotStore()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const createDialogVisible = ref(false)
const copyDialogVisible = ref(false)
const mergeDialogVisible = ref(false)
const editDialogVisible = ref(false)
const createFormRef = ref()
const copyFormRef = ref()
const mergeFormRef = ref()
const editFormRef = ref()

// 表单数据
const createForm = ref({
  name: '',
  systemImage: '',
  dataImage: '',
  description: ''
})

const copyForm = ref({
  originalId: '',
  originalName: '',
  name: '',
  description: ''
})

const mergeForm = ref({
  name: '',
  selectedSnapshots: [],
  description: ''
})

const editForm = ref({
  id: '',
  name: '',
  description: '',
  partitions: []
})

// 表单验证规则
const createRules = {
  name: [{ required: true, message: '请输入快照名称', trigger: 'blur' }],
  systemImage: [{ required: true, message: '请选择系统分区镜像', trigger: 'change' }],
  dataImage: [{ required: true, message: '请选择数据分区镜像', trigger: 'change' }]
}

const copyRules = {
  name: [{ required: true, message: '请输入新快照名称', trigger: 'blur' }]
}

const mergeRules = {
  name: [{ required: true, message: '请输入合并后名称', trigger: 'blur' }],
  selectedSnapshots: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length < 2) {
          callback(new Error('请至少选择2个快照进行合并'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

const editRules = {
  name: [{ required: true, message: '请输入快照名称', trigger: 'blur' }]
}

// 计算属性
const filteredSnapshots = computed(() => {
  if (!searchText.value) {
    return snapshotStore.snapshots
  }
  return snapshotStore.snapshots.filter(snapshot =>
    snapshot.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const availableSnapshots = computed(() => {
  return snapshotStore.snapshots.filter(snapshot => !snapshot.children)
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleRefresh = async () => {
  loading.value = true
  try {
    const data = await getSnapshots()
    snapshotStore.setSnapshots(data)
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleCreateSnapshot = () => {
  createDialogVisible.value = true
}

const handleCreateConfirm = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    const newSnapshot = await createSnapshot(createForm.value)
    snapshotStore.addSnapshot(newSnapshot)
    createDialogVisible.value = false
    ElMessage.success('系统快照创建成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
    }
  }
}

const handleCopy = (row) => {
  copyForm.value = {
    originalId: row.id,
    originalName: row.name,
    name: row.name + '_副本',
    description: row.description
  }
  copyDialogVisible.value = true
}

const handleCopyConfirm = async () => {
  if (!copyFormRef.value) return
  
  try {
    await copyFormRef.value.validate()
    const newSnapshot = await copySnapshot(copyForm.value.originalId, copyForm.value)
    snapshotStore.addSnapshot(newSnapshot)
    copyDialogVisible.value = false
    ElMessage.success('快照复制成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('复制失败')
    }
  }
}

const handleMergeSnapshot = () => {
  mergeDialogVisible.value = true
}

const handleMergeConfirm = async () => {
  if (!mergeFormRef.value) return
  
  try {
    await mergeFormRef.value.validate()
    const mergedSnapshot = await mergeSnapshots(mergeForm.value)
    // 更新快照列表，将合并的快照设为父子结构
    const parentSnapshot = {
      ...mergedSnapshot,
      children: mergeForm.value.selectedSnapshots.map(id => 
        snapshotStore.snapshots.find(s => s.id === id)
      )
    }
    snapshotStore.addSnapshot(parentSnapshot)
    mergeDialogVisible.value = false
    ElMessage.success('系统快照合并成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('合并失败')
    }
  }
}

const handleEditProperties = (row) => {
  editForm.value = {
    id: row.id,
    name: row.name,
    description: row.description,
    partitions: row.partitions || [
      { name: 'C盘 - 系统分区' },
      { name: 'D盘 - 数据分区' },
      { name: 'E盘 - 扩展分区' }
    ]
  }
  editDialogVisible.value = true
}

const handleEditConfirm = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    await updateSnapshot(editForm.value.id, editForm.value)
    snapshotStore.updateSnapshot(editForm.value.id, editForm.value)
    editDialogVisible.value = false
    ElMessage.success('属性更新成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('更新失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除快照"${row.name}"吗？删除后不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSnapshot(row.id)
    snapshotStore.deleteSnapshot(row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleEditColumns = () => {
  ElMessage.info('个性化显示设置功能')
}

const movePartitionUp = (index) => {
  if (index > 0) {
    const partitions = [...editForm.value.partitions]
    const temp = partitions[index]
    partitions[index] = partitions[index - 1]
    partitions[index - 1] = temp
    editForm.value.partitions = partitions
  }
}

const movePartitionDown = (index) => {
  if (index < editForm.value.partitions.length - 1) {
    const partitions = [...editForm.value.partitions]
    const temp = partitions[index]
    partitions[index] = partitions[index + 1]
    partitions[index + 1] = temp
    editForm.value.partitions = partitions
  }
}

const resetCreateForm = () => {
  createForm.value = {
    name: '',
    systemImage: '',
    dataImage: '',
    description: ''
  }
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

const resetCopyForm = () => {
  copyForm.value = {
    originalId: '',
    originalName: '',
    name: '',
    description: ''
  }
  if (copyFormRef.value) {
    copyFormRef.value.resetFields()
  }
}

const resetMergeForm = () => {
  mergeForm.value = {
    name: '',
    selectedSnapshots: [],
    description: ''
  }
  if (mergeFormRef.value) {
    mergeFormRef.value.resetFields()
  }
}

const resetEditForm = () => {
  editForm.value = {
    id: '',
    name: '',
    description: '',
    partitions: []
  }
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const data = await getSnapshots()
    snapshotStore.setSnapshots(data)
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.snapshot-management {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.data-table {
  border-radius: 4px;
  overflow: hidden;
}

.snapshot-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.snapshot-item {
  margin-bottom: 8px;
}

.merge-tip {
  margin-top: 8px;
}

.partition-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.partition-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.partition-item:last-child {
  border-bottom: none;
}

.partition-actions {
  display: flex;
  gap: 5px;
}
</style>
