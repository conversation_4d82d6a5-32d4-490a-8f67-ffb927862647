<template>
  <div class="terminal-management">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/" class="breadcrumb">
      <el-breadcrumb-item>分组管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ currentGroupName || '终端管理' }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handlePresetCommand">
          <el-icon><Setting /></el-icon>
          预设命令
        </el-button>
        <el-button type="success" @click="handleAddPlaceholder">
          <el-icon><Plus /></el-icon>
          添加占位机
        </el-button>
        <el-button type="warning" @click="handleGroupShutdown">
          <el-icon><SwitchButton /></el-icon>
          关机
        </el-button>
        <el-button type="info" @click="handleGroupRestart">
          <el-icon><RefreshRight /></el-icon>
          重启
        </el-button>
        <el-button type="success" @click="handleGroupWakeup">
          <el-icon><VideoPlay /></el-icon>
          唤醒
        </el-button>
        <el-button @click="handleBatchOperation" :type="batchMode ? 'danger' : 'primary'">
          <el-icon><Operation /></el-icon>
          {{ batchMode ? '退出批量操作' : '批量操作' }}
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="handleEditColumns">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchText"
          placeholder="请输入终端名称搜索"
          style="width: 300px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 批量操作工具栏 -->
    <div v-if="batchMode" class="batch-toolbar">
      <div class="batch-actions">
        <el-button size="small" @click="handleSelectOdd">选择单数</el-button>
        <el-button size="small" @click="handleSelectEven">选择双数</el-button>
        <el-button size="small" @click="handleSelectAll">全选</el-button>
        <el-divider direction="vertical" />
        <span class="selected-count">已选择 {{ selectedTerminals.length }} 台终端</span>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="filteredTerminals"
      v-loading="loading"
      stripe
      style="width: 100%"
      class="data-table"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="batchMode" type="selection" width="55" />
      <el-table-column prop="hostNumber" label="主机编号" width="100" />
      <el-table-column prop="name" label="终端名称" min-width="120" />
      <el-table-column prop="ipAddress" label="终端IP地址" width="140" />
      <el-table-column prop="macAddress" label="终端硬件地址" width="150" />
      <el-table-column prop="onlineStatus" label="在线状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.onlineStatus === '在线' ? 'success' : 'danger'">
            {{ row.onlineStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="terminalStatus" label="终端状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.terminalStatus)">
            {{ row.terminalStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="deployMode" label="部署方式" width="100" />
      <el-table-column prop="undeployedData" label="未部署数据" width="120">
        <template #default="{ row }">
          {{ formatSize(row.undeployedData) }}
        </template>
      </el-table-column>
      <el-table-column prop="speed" label="瞬时速度/平均速度(MB/S)" width="180">
        <template #default="{ row }">
          {{ row.instantSpeed }} / {{ row.averageSpeed }}
        </template>
      </el-table-column>
      <el-table-column prop="remainingTime" label="剩余时间(minute)" width="140" />
      <el-table-column prop="presetCommand" label="预设命令" width="120" />
      <el-table-column prop="subGroupNumber" label="子组编号" width="100" />
      <el-table-column prop="controlIP" label="中控IP" width="120" />
      <el-table-column prop="networkSpeed" label="网卡速度" width="100" />
      <el-table-column prop="usedSnapshot" label="使用快照" min-width="150" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEditProperties(row)">
            属性
          </el-button>
          <el-button size="small" type="warning" @click="handleTerminalShutdown(row)">
            关机
          </el-button>
          <el-button size="small" type="info" @click="handleTerminalRestart(row)">
            重启
          </el-button>
          <el-button size="small" type="success" @click="handleTerminalWakeup(row)">
            唤醒
          </el-button>
          <el-button size="small" type="danger" @click="handleDeleteTerminal(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 预设命令对话框 -->
    <el-dialog
      v-model="presetCommandDialogVisible"
      title="设置组内终端预设命令"
      width="500px"
    >
      <el-form :model="presetCommandForm" label-width="120px">
        <el-form-item label="预设命令">
          <el-radio-group v-model="presetCommandForm.command">
            <el-radio label="updateClient">更新客户端底层程序</el-radio>
            <el-radio label="redeployInfo">重新部署信息</el-radio>
            <el-radio label="setCurrentTask">设为当前任务盘\盘数</el-radio>
            <el-radio label="deleteCurrentTask">删除当前任务</el-radio>
            <el-radio label="syncProgram">同步当前程序到系统</el-radio>
            <el-radio label="fullRedeploy">完全重新部署</el-radio>
            <el-radio label="none">无预置命令</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="presetCommandDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePresetCommandConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑终端属性对话框 -->
    <el-dialog
      v-model="editTerminalDialogVisible"
      title="编辑终端属性"
      width="600px"
      @close="resetEditTerminalForm"
    >
      <el-form :model="editTerminalForm" :rules="editTerminalRules" ref="editTerminalFormRef" label-width="150px">
        <el-form-item label="机器MAC地址" prop="macAddress">
          <el-input v-model="editTerminalForm.macAddress" />
        </el-form-item>
        <el-form-item label="机器名称" prop="name">
          <el-input v-model="editTerminalForm.name" />
        </el-form-item>
        <el-form-item label="机器IP地址" prop="ipAddress">
          <el-input v-model="editTerminalForm.ipAddress" />
        </el-form-item>
        <el-form-item label="机器所属组" prop="groupId">
          <el-select v-model="editTerminalForm.groupId" placeholder="点击移动所属分组">
            <el-option label="教学机房1" value="1" />
            <el-option label="教学机房2" value="2" />
            <el-option label="办公室" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="预设命令" prop="presetCommand">
          <el-select v-model="editTerminalForm.presetCommand" placeholder="请选择预设命令">
            <el-option label="更新客户端底层程序" value="updateClient" />
            <el-option label="重新部署信息" value="redeployInfo" />
            <el-option label="设为当前任务盘\盘数" value="setCurrentTask" />
            <el-option label="删除当前任务" value="deleteCurrentTask" />
            <el-option label="同步当前程序到系统" value="syncProgram" />
            <el-option label="完全重新部署" value="fullRedeploy" />
            <el-option label="无预置命令" value="none" />
          </el-select>
        </el-form-item>
        <el-form-item label="机器主机号" prop="hostNumber">
          <el-input-number v-model="editTerminalForm.hostNumber" :min="1" :max="254" />
          <div class="form-tip">用来重新分配IP地址</div>
        </el-form-item>
        <el-form-item label="引用快照" prop="snapshotId">
          <el-select v-model="editTerminalForm.snapshotId" placeholder="请选择快照">
            <el-option label="Win10_Office_2019" value="1" />
            <el-option label="Win7_Dev_Tools" value="2" />
            <el-option label="Ubuntu_20_04_Server" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否强制修改本机网关为组内设置的网关" prop="forceGateway">
          <el-switch v-model="editTerminalForm.forceGateway" />
        </el-form-item>
        <el-form-item label="中控IP" prop="controlIP">
          <el-input v-model="editTerminalForm.controlIP" placeholder="可选" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editTerminalDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditTerminalConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTerminalStore } from '../store'
import { useRoute } from 'vue-router'
import { getTerminals, updateTerminal } from '../mock/api'

const terminalStore = useTerminalStore()
const route = useRoute()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const batchMode = ref(false)
const selectedTerminals = ref([])
const currentGroupName = ref(route.query.groupName || '')

// 对话框显示状态
const presetCommandDialogVisible = ref(false)
const editTerminalDialogVisible = ref(false)

// 表单引用
const editTerminalFormRef = ref()

// 表单数据
const presetCommandForm = ref({
  command: 'none'
})

const editTerminalForm = ref({
  id: '',
  macAddress: '',
  name: '',
  ipAddress: '',
  groupId: '',
  presetCommand: '',
  hostNumber: 1,
  snapshotId: '',
  forceGateway: false,
  controlIP: ''
})

// 表单验证规则
const editTerminalRules = {
  macAddress: [{ required: true, message: '请输入MAC地址', trigger: 'blur' }],
  name: [{ required: true, message: '请输入机器名称', trigger: 'blur' }],
  ipAddress: [{ required: true, message: '请输入IP地址', trigger: 'blur' }]
}

// 计算属性
const filteredTerminals = computed(() => {
  if (!searchText.value) {
    return terminalStore.terminals
  }
  return terminalStore.terminals.filter(terminal =>
    terminal.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 方法
const getStatusType = (status) => {
  const statusMap = {
    '运行中': 'success',
    '部署中': 'warning',
    '离线': 'danger',
    '待机': 'info'
  }
  return statusMap[status] || 'info'
}

const formatSize = (size) => {
  if (size >= 1024) {
    return (size / 1024).toFixed(1) + ' GB'
  }
  return size + ' MB'
}

const handleRefresh = async () => {
  loading.value = true
  try {
    const data = await getTerminals(route.query.groupId)
    terminalStore.setTerminals(data)
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handlePresetCommand = () => {
  presetCommandDialogVisible.value = true
}

const handlePresetCommandConfirm = () => {
  presetCommandDialogVisible.value = false
  ElMessage.success('预设命令设置成功')
}

const handleAddPlaceholder = async () => {
  try {
    await ElMessageBox.confirm(
      '是否添加占位机？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 添加占位机逻辑
    const placeholder = {
      id: Date.now(),
      hostNumber: terminalStore.terminals.length + 1,
      name: `占位机${terminalStore.terminals.length + 1}`,
      ipAddress: '0.0.0.0',
      macAddress: '00:00:00:00:00:00',
      onlineStatus: '离线',
      terminalStatus: '占位',
      deployMode: '-',
      undeployedData: 0,
      instantSpeed: 0,
      averageSpeed: 0,
      remainingTime: 0,
      presetCommand: '无',
      subGroupNumber: 1,
      controlIP: '',
      networkSpeed: '0Mbps',
      usedSnapshot: '-'
    }
    
    terminalStore.terminals.push(placeholder)
    ElMessage.success('占位机添加成功')
  } catch (error) {
    // 用户取消操作
  }
}

const handleGroupShutdown = async () => {
  try {
    await ElMessageBox.confirm(
      '是否全组关机？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('全组关机命令已发送')
  } catch (error) {
    // 用户取消操作
  }
}

const handleGroupRestart = async () => {
  try {
    await ElMessageBox.confirm(
      '是否全组重启？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('全组重启命令已发送')
  } catch (error) {
    // 用户取消操作
  }
}

const handleGroupWakeup = async () => {
  try {
    await ElMessageBox.confirm(
      '是否全组唤醒？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    ElMessage.success('全组唤醒命令已发送')
  } catch (error) {
    // 用户取消操作
  }
}

const handleBatchOperation = () => {
  batchMode.value = !batchMode.value
  if (!batchMode.value) {
    selectedTerminals.value = []
  }
}

const handleSelectionChange = (selection) => {
  selectedTerminals.value = selection
}

const handleSelectOdd = () => {
  // 选择单数行
  const oddTerminals = filteredTerminals.value.filter((_, index) => index % 2 === 0)
  selectedTerminals.value = oddTerminals
}

const handleSelectEven = () => {
  // 选择双数行
  const evenTerminals = filteredTerminals.value.filter((_, index) => index % 2 === 1)
  selectedTerminals.value = evenTerminals
}

const handleSelectAll = () => {
  selectedTerminals.value = [...filteredTerminals.value]
}

const handleEditProperties = (row) => {
  editTerminalForm.value = {
    id: row.id,
    macAddress: row.macAddress,
    name: row.name,
    ipAddress: row.ipAddress,
    groupId: row.groupId || '1',
    presetCommand: row.presetCommand || 'none',
    hostNumber: row.hostNumber,
    snapshotId: row.snapshotId || '',
    forceGateway: row.forceGateway || false,
    controlIP: row.controlIP || ''
  }
  editTerminalDialogVisible.value = true
}

const handleEditTerminalConfirm = async () => {
  if (!editTerminalFormRef.value) return
  
  try {
    await editTerminalFormRef.value.validate()
    await updateTerminal(editTerminalForm.value.id, editTerminalForm.value)
    
    // 更新本地数据
    const index = terminalStore.terminals.findIndex(t => t.id === editTerminalForm.value.id)
    if (index !== -1) {
      terminalStore.terminals[index] = { ...terminalStore.terminals[index], ...editTerminalForm.value }
    }
    
    editTerminalDialogVisible.value = false
    ElMessage.success('终端属性更新成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('更新失败')
    }
  }
}

const handleTerminalShutdown = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要关机终端"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success(`终端"${row.name}"关机命令已发送`)
  } catch (error) {
    // 用户取消操作
  }
}

const handleTerminalRestart = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重启终端"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success(`终端"${row.name}"重启命令已发送`)
  } catch (error) {
    // 用户取消操作
  }
}

const handleTerminalWakeup = async (row) => {
  ElMessage.success(`终端"${row.name}"唤醒命令已发送`)
}

const handleDeleteTerminal = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除终端"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }
    )
    
    // 删除终端
    const index = terminalStore.terminals.findIndex(t => t.id === row.id)
    if (index !== -1) {
      terminalStore.terminals.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleEditColumns = () => {
  ElMessage.info('个性化显示设置功能')
}

const resetEditTerminalForm = () => {
  editTerminalForm.value = {
    id: '',
    macAddress: '',
    name: '',
    ipAddress: '',
    groupId: '',
    presetCommand: '',
    hostNumber: 1,
    snapshotId: '',
    forceGateway: false,
    controlIP: ''
  }
  if (editTerminalFormRef.value) {
    editTerminalFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const data = await getTerminals(route.query.groupId)
    terminalStore.setTerminals(data)
  } catch (error) {
    ElMessage.error('加载终端数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.terminal-management {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.batch-toolbar {
  background: #f5f7fa;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.selected-count {
  color: #409EFF;
  font-weight: 500;
}

.data-table {
  border-radius: 4px;
  overflow: hidden;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
