<template>
  <div class="image-management">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleCreateImage">
          <el-icon><Plus /></el-icon>
          新建分区镜像
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="handleEditColumns">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchText"
          placeholder="请输入镜像名称搜索"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="filteredImages"
      v-loading="loading"
      stripe
      style="width: 100%"
      class="data-table"
    >
      <el-table-column prop="name" label="镜像名称" min-width="150" />
      <el-table-column prop="type" label="镜像类型" width="120" />
      <el-table-column prop="size" label="镜像大小" width="120">
        <template #default="{ row }">
          {{ formatSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEditProperties(row)">
            属性
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建镜像对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建分区镜像"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="选择分区" prop="partition">
          <el-select v-model="createForm.partition" placeholder="请选择要上传的分区">
            <el-option label="C盘 (系统分区)" value="C" />
            <el-option label="D盘 (数据分区)" value="D" />
            <el-option label="E盘 (数据分区)" value="E" />
          </el-select>
        </el-form-item>
        <el-form-item label="镜像名称" prop="name">
          <el-input v-model="createForm.name" placeholder="如：Win7 64 C" />
        </el-form-item>
        <el-form-item label="镜像类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择镜像类型">
            <el-option label="系统分区" value="系统分区" />
            <el-option label="数据分区" value="数据分区" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入镜像描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑属性对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑镜像属性"
      width="500px"
      @close="resetEditForm"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-form-item label="镜像名称" prop="name">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 列显示设置对话框 -->
    <el-dialog
      v-model="columnDialogVisible"
      title="个性化显示设置"
      width="400px"
    >
      <el-checkbox-group v-model="visibleColumns">
        <div v-for="column in allColumns" :key="column.key" style="margin-bottom: 10px;">
          <el-checkbox :label="column.key">{{ column.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="columnDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleColumnConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useImageStore } from '../store'
import { getImages, createImage, updateImage, deleteImage } from '../mock/api'

const imageStore = useImageStore()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const columnDialogVisible = ref(false)
const createFormRef = ref()
const editFormRef = ref()

// 表单数据
const createForm = ref({
  partition: '',
  name: '',
  type: '',
  description: ''
})

const editForm = ref({
  id: '',
  name: '',
  description: ''
})

// 表单验证规则
const createRules = {
  partition: [{ required: true, message: '请选择分区', trigger: 'change' }],
  name: [{ required: true, message: '请输入镜像名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择镜像类型', trigger: 'change' }]
}

const editRules = {
  name: [{ required: true, message: '请输入镜像名称', trigger: 'blur' }]
}

// 列显示设置
const allColumns = [
  { key: 'name', label: '镜像名称' },
  { key: 'type', label: '镜像类型' },
  { key: 'size', label: '镜像大小' },
  { key: 'status', label: '状态' },
  { key: 'createTime', label: '创建时间' },
  { key: 'description', label: '描述' }
]

const visibleColumns = ref(['name', 'type', 'size', 'status', 'createTime', 'description'])

// 计算属性
const filteredImages = computed(() => {
  if (!searchText.value) {
    return imageStore.images
  }
  return imageStore.images.filter(image =>
    image.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 方法
const formatSize = (size) => {
  if (size >= 1024) {
    return (size / 1024).toFixed(1) + ' GB'
  }
  return size + ' MB'
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleRefresh = async () => {
  loading.value = true
  try {
    const data = await getImages()
    imageStore.setImages(data)
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleCreateImage = () => {
  createDialogVisible.value = true
}

const handleCreateConfirm = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    const newImage = await createImage(createForm.value)
    imageStore.addImage(newImage)
    createDialogVisible.value = false
    ElMessage.success('镜像创建成功，开始上传...')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('创建失败')
    }
  }
}

const handleEditProperties = (row) => {
  editForm.value = {
    id: row.id,
    name: row.name,
    description: row.description
  }
  editDialogVisible.value = true
}

const handleEditConfirm = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    await updateImage(editForm.value.id, editForm.value)
    imageStore.updateImage(editForm.value.id, editForm.value)
    editDialogVisible.value = false
    ElMessage.success('属性更新成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('更新失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除镜像"${row.name}"吗？删除后不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteImage(row.id)
    imageStore.deleteImage(row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleEditColumns = () => {
  columnDialogVisible.value = true
}

const handleColumnConfirm = () => {
  columnDialogVisible.value = false
  ElMessage.success('显示设置已更新')
}

const resetCreateForm = () => {
  createForm.value = {
    partition: '',
    name: '',
    type: '',
    description: ''
  }
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

const resetEditForm = () => {
  editForm.value = {
    id: '',
    name: '',
    description: ''
  }
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const data = await getImages()
    imageStore.setImages(data)
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.image-management {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.data-table {
  border-radius: 4px;
  overflow: hidden;
}
</style>
