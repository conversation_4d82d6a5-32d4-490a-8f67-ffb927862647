<template>
  <div class="group-management">
    <el-container>
      <!-- 左侧树形目录 -->
      <el-aside width="300px" class="tree-aside">
        <div class="tree-header">
          <h4>分组目录</h4>
          <el-button size="small" @click="handleEditDirectory" :type="editMode ? 'success' : 'primary'">
            {{ editMode ? '完成编辑' : '编辑目录' }}
          </el-button>
        </div>
        <el-tree
          :data="groupTree"
          :props="treeProps"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          class="directory-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">{{ node.label }}</span>
              <div v-if="editMode" class="node-actions">
                <el-button size="small" @click.stop="handleAddDirectory(data)">+</el-button>
                <el-button size="small" @click.stop="handleDeleteDirectory(data)">-</el-button>
                <el-button size="small" @click.stop="handleRenameDirectory(data)">重命名</el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </el-aside>

      <!-- 右侧内容区 -->
      <el-main class="main-content">
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="handleCreateGroup">
              <el-icon><Plus /></el-icon>
              新建分组
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchText"
              placeholder="请输入分组名称搜索"
              style="width: 300px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 分组卡片列表 -->
        <div class="group-cards" v-loading="loading">
          <div
            v-for="group in filteredGroups"
            :key="group.id"
            class="group-card"
            @click="handleCardClick(group)"
          >
            <div class="card-header">
              <h3>{{ group.name }}</h3>
              <el-button size="small" @click.stop="handleEditGroup(group)">
                编辑分组
              </el-button>
            </div>
            
            <div class="card-content">
              <div class="info-row">
                <span class="label">终端数量：</span>
                <span class="value">{{ group.terminalCount }}</span>
              </div>
              <div class="info-row">
                <span class="label">在线数量：</span>
                <span class="value online">{{ group.onlineCount }}</span>
              </div>
              <div class="info-row">
                <span class="label">部署状态：</span>
                <el-tag :type="getDeployStatusType(group.deployStatus)">
                  {{ group.deployStatus }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="label">磁盘信息：</span>
                <span class="value">{{ group.diskInfo }}</span>
              </div>
              <div class="info-row">
                <span class="label">IP范围：</span>
                <span class="value">{{ group.ipRange }}</span>
              </div>
              <div class="info-row">
                <span class="label">创建时间：</span>
                <span class="value">{{ group.createTime }}</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button size="small" type="success" @click.stop="handleStartDeploy(group)">
                开始部署
              </el-button>
              <el-button size="small" type="warning" @click.stop="handleStopDeploy(group)">
                停止部署
              </el-button>
              <el-button size="small" type="danger" @click.stop="handleDeleteGroup(group)">
                删除分组
              </el-button>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 新建分组对话框 -->
    <el-dialog
      v-model="createGroupDialogVisible"
      title="新建分组"
      width="700px"
      @close="resetCreateGroupForm"
    >
      <el-form :model="createGroupForm" :rules="createGroupRules" ref="createGroupFormRef" label-width="150px">
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="createGroupForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="选择磁盘" prop="diskId">
          <el-select v-model="createGroupForm.diskId" placeholder="请选择磁盘">
            <el-option label="教学磁盘_Win10" value="1" />
            <el-option label="办公磁盘_Win7" value="2" />
            <el-option label="开发磁盘_Ubuntu" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="起始IP地址" prop="startIP">
          <el-input v-model="createGroupForm.startIP" placeholder="如：*************" />
        </el-form-item>
        <el-form-item label="子网掩码" prop="subnetMask">
          <el-input v-model="createGroupForm.subnetMask" placeholder="如：*************" />
        </el-form-item>
        <el-form-item label="分组网关" prop="gateway">
          <el-input v-model="createGroupForm.gateway" placeholder="如：***********" />
        </el-form-item>
        <el-form-item label="首选DNS服务器" prop="primaryDNS">
          <el-input v-model="createGroupForm.primaryDNS" placeholder="如：*******" />
        </el-form-item>
        <el-form-item label="备用DNS服务器" prop="secondaryDNS">
          <el-input v-model="createGroupForm.secondaryDNS" placeholder="如：*******" />
        </el-form-item>
        <el-form-item label="机器名前缀" prop="machinePrefix">
          <el-input v-model="createGroupForm.machinePrefix" placeholder="如：PC" />
        </el-form-item>
        <el-form-item label="机器名起始编号" prop="machineStartNumber">
          <el-input-number v-model="createGroupForm.machineStartNumber" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="是否默认组" prop="isDefault">
          <el-switch v-model="createGroupForm.isDefault" />
          <div class="form-tip">首次创建分组时应将默认组选项打开</div>
        </el-form-item>
        <el-form-item label="开机自动后台部署" prop="autoBackgroundDeploy">
          <el-switch v-model="createGroupForm.autoBackgroundDeploy" />
        </el-form-item>
        <el-form-item label="执行软件预注册" prop="softwarePreRegister">
          <el-switch v-model="createGroupForm.softwarePreRegister" />
        </el-form-item>
        <el-form-item label="系统部署策略" prop="deployStrategy">
          <el-select v-model="createGroupForm.deployStrategy" placeholder="请选择部署策略">
            <el-option label="智能部署" value="smart" />
            <el-option label="强制部署" value="force" />
            <el-option label="按需部署" value="ondemand" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="showAdvancedSettings = !showAdvancedSettings">
            {{ showAdvancedSettings ? '隐藏高级设置' : '高级设置' }}
          </el-button>
        </el-form-item>
        
        <!-- 高级设置 -->
        <div v-if="showAdvancedSettings" class="advanced-settings">
          <el-divider content-position="left">高级部署设置</el-divider>
          <el-form-item label="组内机器部署窗口大小" prop="deployWindowSize">
            <el-input-number v-model="createGroupForm.deployWindowSize" :min="1" :max="100" />
          </el-form-item>
          <el-form-item label="组内机器种子线程数" prop="seedThreads">
            <el-input-number v-model="createGroupForm.seedThreads" :min="1" :max="20" />
          </el-form-item>
          <el-form-item label="部署数据缓冲区大小" prop="deployBufferSize">
            <el-input-number v-model="createGroupForm.deployBufferSize" :min="64" :max="1024" />
            <span style="margin-left: 10px;">MB</span>
          </el-form-item>
          <el-form-item label="部署二级缓冲区大小" prop="secondaryBufferSize">
            <el-input-number v-model="createGroupForm.secondaryBufferSize" :min="32" :max="512" />
            <span style="margin-left: 10px;">MB</span>
          </el-form-item>
          <el-form-item label="磁盘IO缓冲区开始地址" prop="ioBufferStartAddress">
            <el-input v-model="createGroupForm.ioBufferStartAddress" placeholder="如：0x10000000" />
          </el-form-item>
          <el-form-item label="磁盘IO缓冲区大小" prop="ioBufferSize">
            <el-input-number v-model="createGroupForm.ioBufferSize" :min="16" :max="256" />
            <span style="margin-left: 10px;">MB</span>
          </el-form-item>
          <el-form-item label="最低响应时间" prop="minResponseTime">
            <el-input-number v-model="createGroupForm.minResponseTime" :min="100" :max="5000" />
            <span style="margin-left: 10px;">ms</span>
          </el-form-item>
          <el-form-item label="是否按课表要求部署" prop="deployBySchedule">
            <el-switch v-model="createGroupForm.deployBySchedule" />
          </el-form-item>
          <el-form-item label="压缩存储" prop="compressStorage">
            <el-switch v-model="createGroupForm.compressStorage" />
          </el-form-item>
          <el-form-item label="启动DHCP代理" prop="enableDHCPProxy">
            <el-switch v-model="createGroupForm.enableDHCPProxy" />
          </el-form-item>
          <el-form-item label="是否使用第三方DHCP服务器" prop="useThirdPartyDHCP">
            <el-switch v-model="createGroupForm.useThirdPartyDHCP" />
          </el-form-item>
          <el-form-item label="是否可以禁用网卡" prop="canDisableNetworkCard">
            <el-switch v-model="createGroupForm.canDisableNetworkCard" />
          </el-form-item>
          <el-form-item label="代理服务器IP地址" prop="proxyServerIP">
            <el-input v-model="createGroupForm.proxyServerIP" placeholder="可选" />
          </el-form-item>
          <el-form-item label="代理服务器端口" prop="proxyServerPort">
            <el-input-number v-model="createGroupForm.proxyServerPort" :min="1" :max="65535" />
          </el-form-item>
          
          <el-divider content-position="left">显示设置</el-divider>
          <el-form-item label="显示机器名称" prop="showMachineName">
            <el-switch v-model="createGroupForm.showMachineName" />
          </el-form-item>
          <el-form-item label="显示正在使用系统名称" prop="showCurrentSystemName">
            <el-switch v-model="createGroupForm.showCurrentSystemName" />
          </el-form-item>
          <el-form-item label="显示机器MAC地址" prop="showMACAddress">
            <el-switch v-model="createGroupForm.showMACAddress" />
          </el-form-item>
          <el-form-item label="显示机器IP地址" prop="showIPAddress">
            <el-switch v-model="createGroupForm.showIPAddress" />
          </el-form-item>
          <el-form-item label="显示提示信息" prop="showTipInfo">
            <el-switch v-model="createGroupForm.showTipInfo" />
          </el-form-item>
          <el-form-item label="显示器分辨率宽" prop="screenWidth">
            <el-input-number v-model="createGroupForm.screenWidth" :min="800" :max="4096" />
          </el-form-item>
          <el-form-item label="显示器分辨率高" prop="screenHeight">
            <el-input-number v-model="createGroupForm.screenHeight" :min="600" :max="2160" />
          </el-form-item>
          <el-form-item label="显示器刷新频率" prop="refreshRate">
            <el-input-number v-model="createGroupForm.refreshRate" :min="60" :max="144" />
            <span style="margin-left: 10px;">Hz</span>
          </el-form-item>
          <el-form-item label="缩放比例百分比" prop="scalePercentage">
            <el-input-number v-model="createGroupForm.scalePercentage" :min="50" :max="200" />
            <span style="margin-left: 10px;">%</span>
          </el-form-item>
          <el-form-item label="开机屏幕复制" prop="bootScreenCopy">
            <el-switch v-model="createGroupForm.bootScreenCopy" />
          </el-form-item>
          <el-form-item label="开机屏幕扩展" prop="bootScreenExtend">
            <el-switch v-model="createGroupForm.bootScreenExtend" />
          </el-form-item>
          <el-form-item label="开机恢复图标" prop="bootRestoreIcon">
            <el-switch v-model="createGroupForm.bootRestoreIcon" />
          </el-form-item>
          <el-form-item label="开机等待时间" prop="bootWaitTime">
            <el-input-number v-model="createGroupForm.bootWaitTime" :min="0" :max="300" />
            <span style="margin-left: 10px;">秒</span>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleRestoreDefaults">恢复默认设置</el-button>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="createGroupDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateGroupConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGroupStore } from '../store'
import { useRouter } from 'vue-router'
import { getGroupTree, getGroups, createGroup } from '../mock/api'

const groupStore = useGroupStore()
const router = useRouter()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const editMode = ref(false)
const createGroupDialogVisible = ref(false)
const showAdvancedSettings = ref(false)
const createGroupFormRef = ref()

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 表单数据
const createGroupForm = ref({
  name: '',
  diskId: '',
  startIP: '*************',
  subnetMask: '*************',
  gateway: '***********',
  primaryDNS: '*******',
  secondaryDNS: '*******',
  machinePrefix: 'PC',
  machineStartNumber: 1,
  isDefault: false,
  autoBackgroundDeploy: true,
  softwarePreRegister: false,
  deployStrategy: 'smart',
  // 高级设置
  deployWindowSize: 10,
  seedThreads: 5,
  deployBufferSize: 200,
  secondaryBufferSize: 100,
  ioBufferStartAddress: '0x10000000',
  ioBufferSize: 64,
  minResponseTime: 1000,
  deployBySchedule: false,
  compressStorage: true,
  enableDHCPProxy: false,
  useThirdPartyDHCP: false,
  canDisableNetworkCard: false,
  proxyServerIP: '',
  proxyServerPort: 8080,
  showMachineName: true,
  showCurrentSystemName: true,
  showMACAddress: false,
  showIPAddress: true,
  showTipInfo: true,
  screenWidth: 1920,
  screenHeight: 1080,
  refreshRate: 60,
  scalePercentage: 100,
  bootScreenCopy: false,
  bootScreenExtend: false,
  bootRestoreIcon: true,
  bootWaitTime: 10
})

// 表单验证规则
const createGroupRules = {
  name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
  diskId: [{ required: true, message: '请选择磁盘', trigger: 'change' }],
  startIP: [{ required: true, message: '请输入起始IP地址', trigger: 'blur' }],
  subnetMask: [{ required: true, message: '请输入子网掩码', trigger: 'blur' }],
  gateway: [{ required: true, message: '请输入分组网关', trigger: 'blur' }]
}

// 计算属性
const groupTree = computed(() => groupStore.groupTree)
const filteredGroups = computed(() => {
  if (!searchText.value) {
    return groupStore.groups
  }
  return groupStore.groups.filter(group =>
    group.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 方法
const getDeployStatusType = (status) => {
  const statusMap = {
    '部署中': 'warning',
    '已完成': 'success',
    '未部署': 'info',
    '部署失败': 'danger'
  }
  return statusMap[status] || 'info'
}

const handleNodeClick = async (data) => {
  if (data.type === 'directory') {
    loading.value = true
    try {
      const groups = await getGroups(data.id)
      groupStore.setGroups(groups)
    } catch (error) {
      ElMessage.error('加载分组列表失败')
    } finally {
      loading.value = false
    }
  }
}

const handleEditDirectory = () => {
  editMode.value = !editMode.value
}

const handleAddDirectory = (data) => {
  ElMessage.info('新建目录功能')
}

const handleDeleteDirectory = (data) => {
  ElMessage.info('删除目录功能')
}

const handleRenameDirectory = (data) => {
  ElMessage.info('重命名目录功能')
}

const handleRefresh = async () => {
  loading.value = true
  try {
    const groups = await getGroups()
    groupStore.setGroups(groups)
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleCreateGroup = () => {
  createGroupDialogVisible.value = true
}

const handleCreateGroupConfirm = async () => {
  if (!createGroupFormRef.value) return
  
  try {
    await createGroupFormRef.value.validate()
    const newGroup = await createGroup(createGroupForm.value)
    groupStore.groups.push(newGroup)
    createGroupDialogVisible.value = false
    ElMessage.success('分组创建成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
    }
  }
}

const handleEditGroup = (group) => {
  ElMessage.info('编辑分组功能')
}

const handleCardClick = (group) => {
  // 跳转到终端管理页面
  router.push({
    path: '/terminal-management',
    query: { groupId: group.id, groupName: group.name }
  })
}

const handleStartDeploy = (group) => {
  ElMessage.success(`开始部署分组"${group.name}"`)
}

const handleStopDeploy = (group) => {
  ElMessage.success(`停止部署分组"${group.name}"`)
}

const handleDeleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分组"${group.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleRestoreDefaults = () => {
  // 恢复高级设置的默认值
  Object.assign(createGroupForm.value, {
    deployWindowSize: 10,
    seedThreads: 5,
    deployBufferSize: 200,
    secondaryBufferSize: 100,
    ioBufferStartAddress: '0x10000000',
    ioBufferSize: 64,
    minResponseTime: 1000,
    deployBySchedule: false,
    compressStorage: true,
    enableDHCPProxy: false,
    useThirdPartyDHCP: false,
    canDisableNetworkCard: false,
    proxyServerIP: '',
    proxyServerPort: 8080,
    showMachineName: true,
    showCurrentSystemName: true,
    showMACAddress: false,
    showIPAddress: true,
    showTipInfo: true,
    screenWidth: 1920,
    screenHeight: 1080,
    refreshRate: 60,
    scalePercentage: 100,
    bootScreenCopy: false,
    bootScreenExtend: false,
    bootRestoreIcon: true,
    bootWaitTime: 10
  })
  ElMessage.success('已恢复默认设置')
}

const resetCreateGroupForm = () => {
  createGroupForm.value = {
    name: '',
    diskId: '',
    startIP: '*************',
    subnetMask: '*************',
    gateway: '***********',
    primaryDNS: '*******',
    secondaryDNS: '*******',
    machinePrefix: 'PC',
    machineStartNumber: 1,
    isDefault: false,
    autoBackgroundDeploy: true,
    softwarePreRegister: false,
    deployStrategy: 'smart',
    deployWindowSize: 10,
    seedThreads: 5,
    deployBufferSize: 200,
    secondaryBufferSize: 100,
    ioBufferStartAddress: '0x10000000',
    ioBufferSize: 64,
    minResponseTime: 1000,
    deployBySchedule: false,
    compressStorage: true,
    enableDHCPProxy: false,
    useThirdPartyDHCP: false,
    canDisableNetworkCard: false,
    proxyServerIP: '',
    proxyServerPort: 8080,
    showMachineName: true,
    showCurrentSystemName: true,
    showMACAddress: false,
    showIPAddress: true,
    showTipInfo: true,
    screenWidth: 1920,
    screenHeight: 1080,
    refreshRate: 60,
    scalePercentage: 100,
    bootScreenCopy: false,
    bootScreenExtend: false,
    bootRestoreIcon: true,
    bootWaitTime: 10
  }
  showAdvancedSettings.value = false
  if (createGroupFormRef.value) {
    createGroupFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const tree = await getGroupTree()
    groupStore.setGroupTree(tree)
    const groups = await getGroups()
    groupStore.setGroups(groups)
  } catch (error) {
    ElMessage.error('加载分组数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.group-management {
  background: white;
  border-radius: 4px;
  height: calc(100vh - 120px);
}

.tree-aside {
  border-right: 1px solid #e4e7ed;
  padding: 20px;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tree-header h4 {
  margin: 0;
}

.directory-tree {
  background: transparent;
  border: none;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
}

.node-actions {
  display: flex;
  gap: 5px;
}

.main-content {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.group-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.group-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.group-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409EFF;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.card-content {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  color: #606266;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.value.online {
  color: #67C23A;
}

.card-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.advanced-settings {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 4px;
  margin-top: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
