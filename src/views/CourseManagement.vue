<template>
  <div class="course-management">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleCreateCourse">
          <el-icon><Plus /></el-icon>
          新建课程
        </el-button>
        <el-button type="info" @click="handleCustomSchedule">
          <el-icon><Setting /></el-icon>
          自定义课程表
        </el-button>
        <el-button @click="handleSwitchView">
          <el-icon><Switch /></el-icon>
          {{ viewMode === 'table' ? '切换列表' : '切换卡片' }}
        </el-button>
      </div>
      <div class="toolbar-right" v-if="viewMode === 'list'">
        <el-input
          v-model="searchText"
          placeholder="请输入课程名称搜索"
          style="width: 300px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 课程表视图 -->
    <div v-if="viewMode === 'table'" class="schedule-view">
      <!-- 周导航 -->
      <div class="week-navigation">
        <el-button @click="handlePreviousWeek">
          <el-icon><ArrowLeft /></el-icon>
          上一周
        </el-button>
        <span class="current-week">{{ currentYear }}年第{{ currentWeek }}周</span>
        <el-button @click="handleNextWeek">
          下一周
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>

      <!-- 课程表格 -->
      <div class="schedule-table">
        <table class="course-table">
          <thead>
            <tr>
              <th class="time-column">时间</th>
              <th v-for="day in weekDays" :key="day">{{ day }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="timeSlot in timeSlots" :key="timeSlot.id">
              <td class="time-cell">{{ timeSlot.time }}</td>
              <td
                v-for="day in weekDays"
                :key="day"
                class="course-cell"
                @click="handleCellClick(timeSlot, day)"
              >
                <div
                  v-for="course in getCoursesByTimeAndDay(timeSlot.id, day)"
                  :key="course.id"
                  :class="getCourseCardClass(course)"
                  class="course-card"
                  @click.stop="handleCourseClick(course)"
                >
                  <div class="course-name">{{ course.name }}</div>
                  <div class="course-classroom">{{ course.classroom }}</div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="list-view">
      <el-table
        :data="filteredCourses"
        v-loading="loading"
        stripe
        style="width: 100%"
        class="data-table"
      >
        <el-table-column prop="dayOfWeek" label="星期" width="80" />
        <el-table-column prop="classroom" label="教室（即分组）" min-width="150" />
        <el-table-column prop="timeSlot" label="上课时间" width="120" />
        <el-table-column prop="system" label="上课系统（单选快照）" min-width="180" />
        <el-table-column prop="stayTime" label="送单停留时间" width="120" />
        <el-table-column prop="courseDescription" label="课堂描述" min-width="200" />
        <el-table-column prop="classroomDescription" label="教室描述" min-width="200" />
        <el-table-column prop="deployToLocal" label="是否部署到本地磁盘" width="150">
          <template #default="{ row }">
            <el-tag :type="row.deployToLocal ? 'success' : 'info'">
              {{ row.deployToLocal ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEditCourse(row)">
              属性
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteCourse(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新建/编辑课程对话框 -->
    <el-dialog
      v-model="courseDialogVisible"
      :title="isEditMode ? '编辑课程' : '新建课程'"
      width="600px"
      @close="resetCourseForm"
    >
      <el-form :model="courseForm" :rules="courseRules" ref="courseFormRef" label-width="120px">
        <el-form-item label="星期" prop="dayOfWeek">
          <el-select v-model="courseForm.dayOfWeek" placeholder="请选择星期">
            <el-option label="周一" value="周一" />
            <el-option label="周二" value="周二" />
            <el-option label="周三" value="周三" />
            <el-option label="周四" value="周四" />
            <el-option label="周五" value="周五" />
            <el-option label="周六" value="周六" />
            <el-option label="周日" value="周日" />
          </el-select>
        </el-form-item>
        <el-form-item label="教室（即分组）" prop="classroom">
          <el-select v-model="courseForm.classroom" placeholder="请选择教室">
            <el-option label="教学机房1" value="教学机房1" />
            <el-option label="教学机房2" value="教学机房2" />
            <el-option label="办公室" value="办公室" />
            <el-option label="实验室" value="实验室" />
          </el-select>
        </el-form-item>
        <el-form-item label="上课时间" prop="timeSlot">
          <el-select v-model="courseForm.timeSlot" placeholder="请选择时间段">
            <el-option v-for="slot in timeSlots" :key="slot.id" :label="slot.time" :value="slot.time" />
          </el-select>
        </el-form-item>
        <el-form-item label="上课系统" prop="system">
          <el-select v-model="courseForm.system" placeholder="请选择快照">
            <el-option label="Win10_Office_2019" value="Win10_Office_2019" />
            <el-option label="Win7_Dev_Tools" value="Win7_Dev_Tools" />
            <el-option label="Ubuntu_20_04_Server" value="Ubuntu_20_04_Server" />
          </el-select>
        </el-form-item>
        <el-form-item label="送单停留时间" prop="stayTime">
          <el-input-number v-model="courseForm.stayTime" :min="0" :max="300" />
          <span style="margin-left: 10px;">分钟</span>
        </el-form-item>
        <el-form-item label="课堂描述" prop="courseDescription">
          <el-input
            v-model="courseForm.courseDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入课堂描述"
          />
        </el-form-item>
        <el-form-item label="教室描述" prop="classroomDescription">
          <el-input
            v-model="courseForm.classroomDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入教室描述"
          />
        </el-form-item>
        <el-form-item label="是否部署到本地磁盘" prop="deployToLocal">
          <el-switch v-model="courseForm.deployToLocal" />
        </el-form-item>
        <el-form-item v-if="!isEditMode">
          <el-button type="info" @click="handleQuickCreate">快速创建课程</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="courseDialogVisible = false">取消</el-button>
        <el-button v-if="isEditMode" type="danger" @click="handleDeleteCurrentCourse">删除</el-button>
        <el-button type="primary" @click="handleCourseConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 自定义课程表对话框 -->
    <el-dialog
      v-model="customScheduleDialogVisible"
      title="自定义课程表"
      width="600px"
      @close="resetCustomScheduleForm"
    >
      <el-form :model="customScheduleForm" :rules="customScheduleRules" ref="customScheduleFormRef" label-width="150px">
        <el-form-item label="每日总课时(节)" prop="totalClasses">
          <el-input-number v-model="customScheduleForm.totalClasses" :min="1" :max="20" />
        </el-form-item>
        <el-form-item label="首节课开始时间" prop="firstClassTime">
          <el-time-picker
            v-model="customScheduleForm.firstClassTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="请选择时间"
          />
        </el-form-item>
        <el-form-item label="单节课时长" prop="classDuration">
          <el-input-number v-model="customScheduleForm.classDuration" :min="30" :max="120" />
          <span style="margin-left: 10px;">分钟</span>
        </el-form-item>
        <el-form-item label="基础课间休息" prop="basicBreak">
          <el-input-number v-model="customScheduleForm.basicBreak" :min="5" :max="30" />
          <span style="margin-left: 10px;">分钟</span>
        </el-form-item>
        <el-form-item label="特殊课间休息">
          <div v-for="(breakItem, index) in customScheduleForm.specialBreaks" :key="index" class="special-break-item">
            <span>第</span>
            <el-input-number v-model="breakItem.afterClass" :min="1" :max="20" size="small" />
            <span>节课后休息</span>
            <el-input-number v-model="breakItem.duration" :min="5" :max="120" size="small" />
            <span>分钟</span>
            <el-button size="small" type="danger" @click="removeSpecialBreak(index)">删除</el-button>
          </div>
          <el-button size="small" @click="addSpecialBreak">添加特殊课间休息</el-button>
        </el-form-item>
        <el-form-item label="午休时间">
          <el-time-picker
            v-model="customScheduleForm.lunchBreakStart"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="开始时间"
            style="width: 120px; margin-right: 10px;"
          />
          <span>至</span>
          <el-time-picker
            v-model="customScheduleForm.lunchBreakEnd"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="结束时间"
            style="width: 120px; margin-left: 10px;"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="customScheduleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCustomScheduleConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 快速创建课程对话框 -->
    <el-dialog
      v-model="quickCreateDialogVisible"
      title="快速创建课程"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item label="选择已有课程">
          <el-select v-model="selectedExistingCourse" placeholder="请选择已有课程">
            <el-option
              v-for="course in existingCourses"
              :key="course.id"
              :label="`${course.name} - ${course.classroom}`"
              :value="course.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="quickCreateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleQuickCreateConfirm">导入课程信息</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCourseStore } from '../store'
import { getCourses, createCourse, updateCourse, deleteCourse } from '../mock/api'

const courseStore = useCourseStore()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const viewMode = ref('table') // 'table' | 'list'
const currentWeek = ref(1)
const currentYear = ref(new Date().getFullYear())

// 对话框显示状态
const courseDialogVisible = ref(false)
const customScheduleDialogVisible = ref(false)
const quickCreateDialogVisible = ref(false)
const isEditMode = ref(false)
const selectedTimeSlot = ref(null)
const selectedDay = ref('')
const selectedExistingCourse = ref('')

// 表单引用
const courseFormRef = ref()
const customScheduleFormRef = ref()

// 基础数据
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const timeSlots = ref([
  { id: 1, time: '08:00-09:00' },
  { id: 2, time: '09:10-10:10' },
  { id: 3, time: '10:20-11:20' },
  { id: 4, time: '11:30-12:30' },
  { id: 5, time: '14:00-15:00' },
  { id: 6, time: '15:10-16:10' },
  { id: 7, time: '16:20-17:20' },
  { id: 8, time: '17:30-18:30' }
])

// 表单数据
const courseForm = ref({
  name: '',
  dayOfWeek: '',
  classroom: '',
  timeSlot: '',
  system: '',
  stayTime: 5,
  courseDescription: '',
  classroomDescription: '',
  deployToLocal: false
})

const customScheduleForm = ref({
  totalClasses: 8,
  firstClassTime: '08:00',
  classDuration: 60,
  basicBreak: 10,
  specialBreaks: [],
  lunchBreakStart: '12:30',
  lunchBreakEnd: '14:00'
})

// 表单验证规则
const courseRules = {
  dayOfWeek: [{ required: true, message: '请选择星期', trigger: 'change' }],
  classroom: [{ required: true, message: '请选择教室', trigger: 'change' }],
  timeSlot: [{ required: true, message: '请选择时间段', trigger: 'change' }],
  system: [{ required: true, message: '请选择上课系统', trigger: 'change' }]
}

const customScheduleRules = {
  totalClasses: [{ required: true, message: '请输入总课时', trigger: 'blur' }],
  firstClassTime: [{ required: true, message: '请选择首节课开始时间', trigger: 'change' }],
  classDuration: [{ required: true, message: '请输入单节课时长', trigger: 'blur' }],
  basicBreak: [{ required: true, message: '请输入基础课间休息时间', trigger: 'blur' }]
}

// 计算属性
const filteredCourses = computed(() => {
  if (!searchText.value) {
    return courseStore.courses
  }
  return courseStore.courses.filter(course =>
    course.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const existingCourses = computed(() => courseStore.courses)

// 方法
const getCoursesByTimeAndDay = (timeSlotId, day) => {
  return courseStore.courses.filter(course => 
    course.timeSlotId === timeSlotId && course.dayOfWeek === day
  )
}

const getCourseCardClass = (course) => {
  if (course.status === 'ongoing') {
    return 'course-card-ongoing'
  } else if (course.status === 'selected') {
    return 'course-card-selected'
  } else {
    return 'course-card-scheduled'
  }
}

const handleSwitchView = () => {
  viewMode.value = viewMode.value === 'table' ? 'list' : 'table'
}

const handlePreviousWeek = () => {
  if (currentWeek.value > 1) {
    currentWeek.value--
  } else {
    currentWeek.value = 52
    currentYear.value--
  }
}

const handleNextWeek = () => {
  if (currentWeek.value < 52) {
    currentWeek.value++
  } else {
    currentWeek.value = 1
    currentYear.value++
  }
}

const handleCellClick = (timeSlot, day) => {
  // 快速新建课程
  selectedTimeSlot.value = timeSlot
  selectedDay.value = day
  courseForm.value.dayOfWeek = day
  courseForm.value.timeSlot = timeSlot.time
  isEditMode.value = false
  courseDialogVisible.value = true
}

const handleCourseClick = (course) => {
  // 编辑课程
  courseForm.value = { ...course }
  isEditMode.value = true
  courseDialogVisible.value = true
}

const handleCreateCourse = () => {
  isEditMode.value = false
  courseDialogVisible.value = true
}

const handleEditCourse = (row) => {
  courseForm.value = { ...row }
  isEditMode.value = true
  courseDialogVisible.value = true
}

const handleCourseConfirm = async () => {
  if (!courseFormRef.value) return
  
  try {
    await courseFormRef.value.validate()
    
    if (isEditMode.value) {
      await updateCourse(courseForm.value.id, courseForm.value)
      courseStore.updateCourse(courseForm.value.id, courseForm.value)
      ElMessage.success('课程更新成功')
    } else {
      const newCourse = await createCourse(courseForm.value)
      courseStore.addCourse(newCourse)
      ElMessage.success('课程创建成功')
    }
    
    courseDialogVisible.value = false
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEditMode.value ? '更新失败' : '创建失败')
    }
  }
}

const handleDeleteCourse = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除课程"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteCourse(row.id)
    courseStore.deleteCourse(row.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleDeleteCurrentCourse = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除该课程吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteCourse(courseForm.value.id)
    courseStore.deleteCourse(courseForm.value.id)
    courseDialogVisible.value = false
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleCustomSchedule = () => {
  customScheduleDialogVisible.value = true
}

const handleCustomScheduleConfirm = async () => {
  if (!customScheduleFormRef.value) return
  
  try {
    await customScheduleFormRef.value.validate()
    
    // 根据自定义设置重新计算时间段
    generateTimeSlots()
    
    customScheduleDialogVisible.value = false
    ElMessage.success('课程表设置已更新')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('设置失败')
    }
  }
}

const generateTimeSlots = () => {
  const slots = []
  const startTime = customScheduleForm.value.firstClassTime
  const classDuration = customScheduleForm.value.classDuration
  const basicBreak = customScheduleForm.value.basicBreak
  const totalClasses = customScheduleForm.value.totalClasses
  
  let currentTime = new Date(`2000-01-01 ${startTime}:00`)
  
  for (let i = 0; i < totalClasses; i++) {
    const startHour = currentTime.getHours().toString().padStart(2, '0')
    const startMinute = currentTime.getMinutes().toString().padStart(2, '0')
    
    currentTime.setMinutes(currentTime.getMinutes() + classDuration)
    
    const endHour = currentTime.getHours().toString().padStart(2, '0')
    const endMinute = currentTime.getMinutes().toString().padStart(2, '0')
    
    slots.push({
      id: i + 1,
      time: `${startHour}:${startMinute}-${endHour}:${endMinute}`
    })
    
    // 添加课间休息
    if (i < totalClasses - 1) {
      const specialBreak = customScheduleForm.value.specialBreaks.find(b => b.afterClass === i + 1)
      const breakDuration = specialBreak ? specialBreak.duration : basicBreak
      currentTime.setMinutes(currentTime.getMinutes() + breakDuration)
    }
  }
  
  timeSlots.value = slots
}

const addSpecialBreak = () => {
  customScheduleForm.value.specialBreaks.push({
    afterClass: 1,
    duration: 30
  })
}

const removeSpecialBreak = (index) => {
  customScheduleForm.value.specialBreaks.splice(index, 1)
}

const handleQuickCreate = () => {
  quickCreateDialogVisible.value = true
}

const handleQuickCreateConfirm = () => {
  if (!selectedExistingCourse.value) {
    ElMessage.warning('请选择已有课程')
    return
  }
  
  const existingCourse = courseStore.courses.find(c => c.id === selectedExistingCourse.value)
  if (existingCourse) {
    Object.assign(courseForm.value, {
      ...existingCourse,
      id: undefined, // 清除ID，作为新课程
      name: existingCourse.name + '_副本'
    })
  }
  
  quickCreateDialogVisible.value = false
  ElMessage.success('课程信息已导入')
}

const resetCourseForm = () => {
  courseForm.value = {
    name: '',
    dayOfWeek: '',
    classroom: '',
    timeSlot: '',
    system: '',
    stayTime: 5,
    courseDescription: '',
    classroomDescription: '',
    deployToLocal: false
  }
  if (courseFormRef.value) {
    courseFormRef.value.resetFields()
  }
}

const resetCustomScheduleForm = () => {
  customScheduleForm.value = {
    totalClasses: 8,
    firstClassTime: '08:00',
    classDuration: 60,
    basicBreak: 10,
    specialBreaks: [],
    lunchBreakStart: '12:30',
    lunchBreakEnd: '14:00'
  }
  if (customScheduleFormRef.value) {
    customScheduleFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const data = await getCourses()
    courseStore.setCourses(data)
  } catch (error) {
    ElMessage.error('加载课程数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.course-management {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.schedule-view {
  width: 100%;
}

.week-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.current-week {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.schedule-table {
  overflow-x: auto;
}

.course-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
}

.course-table th,
.course-table td {
  border: 1px solid #e4e7ed;
  padding: 8px;
  text-align: center;
  vertical-align: top;
}

.course-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.time-column {
  width: 120px;
}

.time-cell {
  background-color: #fafafa;
  font-weight: 500;
}

.course-cell {
  height: 80px;
  cursor: pointer;
  position: relative;
}

.course-cell:hover {
  background-color: #f0f9ff;
}

.course-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px;
  border-radius: 4px;
  margin: 2px;
  cursor: pointer;
  transition: all 0.3s;
}

.course-card-scheduled {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.course-card-selected {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.course-card-ongoing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  animation: breathing 2s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.course-name {
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 4px;
}

.course-classroom {
  font-size: 11px;
  opacity: 0.9;
}

.data-table {
  border-radius: 4px;
  overflow: hidden;
}

.special-break-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
</style>
