<template>
  <div class="system-settings">
    <div class="settings-grid">
      <!-- 服务器配置卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Server /></el-icon>
            <span>服务器配置</span>
          </div>
        </template>
        <div class="card-content">
          <el-form :model="serverConfig" label-width="150px" size="small">
            <el-form-item label="服务器IP地址">
              <el-input v-model="serverConfig.serverIP" placeholder="***********" />
            </el-form-item>
            <el-form-item label="服务器端口">
              <el-input-number v-model="serverConfig.serverPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="数据库连接">
              <el-input v-model="serverConfig.databaseUrl" placeholder="数据库连接字符串" />
            </el-form-item>
            <el-form-item label="最大连接数">
              <el-input-number v-model="serverConfig.maxConnections" :min="10" :max="1000" />
            </el-form-item>
            <el-form-item label="连接超时时间">
              <el-input-number v-model="serverConfig.connectionTimeout" :min="5" :max="300" />
              <span style="margin-left: 10px;">秒</span>
            </el-form-item>
          </el-form>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="handleSaveServerConfig">
              保存配置
            </el-button>
            <el-button size="small" @click="handleTestConnection">
              测试连接
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 运行参数配置卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>运行参数配置</span>
          </div>
        </template>
        <div class="card-content">
          <el-form :model="runtimeConfig" label-width="150px" size="small">
            <el-form-item label="实时数据缓存区大小">
              <el-input-number v-model="runtimeConfig.realtimeCacheSize" :min="64" :max="1024" />
              <span style="margin-left: 10px;">MB</span>
              <div class="config-tip">默认为200MB，一般不需要改变</div>
            </el-form-item>
            <el-form-item label="部署数据缓存区大小">
              <el-input-number v-model="runtimeConfig.deployCacheSize" :min="64" :max="1024" />
              <span style="margin-left: 10px;">MB</span>
              <div class="config-tip">默认为200MB，一般不需要改变</div>
            </el-form-item>
            <el-form-item label="自动选择组部署">
              <el-switch v-model="runtimeConfig.autoSelectGroupDeploy" />
              <div class="config-tip">开启后只部署同一个分组的数据，关闭则可同时部署不同分组</div>
            </el-form-item>
            <el-form-item label="最大并发部署数">
              <el-input-number v-model="runtimeConfig.maxConcurrentDeploy" :min="1" :max="100" />
              <div class="config-tip">控制同时进行部署的终端数量</div>
            </el-form-item>
            <el-form-item label="部署超时时间">
              <el-input-number v-model="runtimeConfig.deployTimeout" :min="60" :max="3600" />
              <span style="margin-left: 10px;">秒</span>
              <div class="config-tip">设置部署操作的最大等待时间</div>
            </el-form-item>
          </el-form>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="handleApplyRuntimeSettings">
              应用设置
            </el-button>
            <el-button size="small" @click="handleRestoreRuntimeDefaults">
              恢复默认
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 服务管理卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>服务管理</span>
          </div>
        </template>
        <div class="card-content">
          <div class="service-list">
            <div v-for="service in services" :key="service.name" class="service-item">
              <div class="service-info">
                <div class="service-name">{{ service.name }}</div>
                <div class="service-description">{{ service.description }}</div>
                <div class="service-status">
                  <el-tag :type="service.status === 'running' ? 'success' : 'danger'">
                    {{ service.status === 'running' ? '运行中' : '已停止' }}
                  </el-tag>
                </div>
              </div>
              <div class="service-actions">
                <el-button
                  v-if="service.status === 'stopped'"
                  type="success"
                  size="small"
                  @click="handleStartService(service)"
                >
                  启动
                </el-button>
                <el-button
                  v-if="service.status === 'running'"
                  type="warning"
                  size="small"
                  @click="handleStopService(service)"
                >
                  停止
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleRestartService(service)"
                >
                  重启
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 扩展存储配置卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><FolderOpened /></el-icon>
            <span>扩展存储配置</span>
          </div>
        </template>
        <div class="card-content">
          <el-form :model="storageConfig" label-width="150px" size="small">
            <el-form-item label="镜像存储路径">
              <el-input v-model="storageConfig.imagePath" placeholder="D:\HaiguangDesk\Images" />
              <el-button size="small" style="margin-left: 10px;" @click="handleBrowseImagePath">
                浏览
              </el-button>
            </el-form-item>
            <el-form-item label="快照存储路径">
              <el-input v-model="storageConfig.snapshotPath" placeholder="D:\HaiguangDesk\Snapshots" />
              <el-button size="small" style="margin-left: 10px;" @click="handleBrowseSnapshotPath">
                浏览
              </el-button>
            </el-form-item>
            <el-form-item label="临时文件路径">
              <el-input v-model="storageConfig.tempPath" placeholder="D:\HaiguangDesk\Temp" />
              <el-button size="small" style="margin-left: 10px;" @click="handleBrowseTempPath">
                浏览
              </el-button>
            </el-form-item>
            <el-form-item label="日志文件路径">
              <el-input v-model="storageConfig.logPath" placeholder="D:\HaiguangDesk\Logs" />
              <el-button size="small" style="margin-left: 10px;" @click="handleBrowseLogPath">
                浏览
              </el-button>
            </el-form-item>
            <el-form-item label="自动清理临时文件">
              <el-switch v-model="storageConfig.autoCleanTemp" />
            </el-form-item>
            <el-form-item label="清理间隔" v-if="storageConfig.autoCleanTemp">
              <el-input-number v-model="storageConfig.cleanInterval" :min="1" :max="30" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
            <el-form-item label="最大日志文件大小">
              <el-input-number v-model="storageConfig.maxLogSize" :min="10" :max="1000" />
              <span style="margin-left: 10px;">MB</span>
            </el-form-item>
          </el-form>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="handleSaveStorageConfig">
              保存配置
            </el-button>
            <el-button size="small" @click="handleCheckDiskSpace">
              检查磁盘空间
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 系统信息卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>系统信息</span>
          </div>
        </template>
        <div class="card-content">
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">海光Desk系统管理版 v{{ systemInfo.version }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器操作系统：</span>
              <span class="info-value">{{ systemInfo.serverOS }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">CPU使用率：</span>
              <span class="info-value">{{ systemInfo.cpuUsage }}%</span>
              <el-progress :percentage="systemInfo.cpuUsage" :show-text="false" style="width: 100px; margin-left: 10px;" />
            </div>
            <div class="info-item">
              <span class="info-label">内存使用率：</span>
              <span class="info-value">{{ systemInfo.memoryUsage }}%</span>
              <el-progress :percentage="systemInfo.memoryUsage" :show-text="false" style="width: 100px; margin-left: 10px;" />
            </div>
            <div class="info-item">
              <span class="info-label">磁盘使用率：</span>
              <span class="info-value">{{ systemInfo.diskUsage }}%</span>
              <el-progress :percentage="systemInfo.diskUsage" :show-text="false" style="width: 100px; margin-left: 10px;" />
            </div>
            <div class="info-item">
              <span class="info-label">运行时间：</span>
              <span class="info-value">{{ systemInfo.uptime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前连接数：</span>
              <span class="info-value">{{ systemInfo.currentConnections }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后更新：</span>
              <span class="info-value">{{ systemInfo.lastUpdate }}</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="handleRefreshSystemInfo">
              刷新信息
            </el-button>
            <el-button size="small" @click="handleExportSystemInfo">
              导出报告
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 许可证信息卡片 -->
      <el-card class="setting-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Key /></el-icon>
            <span>许可证信息</span>
          </div>
        </template>
        <div class="card-content">
          <div class="license-info">
            <div class="info-item">
              <span class="info-label">许可证类型：</span>
              <span class="info-value">{{ licenseInfo.type }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">授权用户数：</span>
              <span class="info-value">{{ licenseInfo.maxUsers }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前用户数：</span>
              <span class="info-value">{{ licenseInfo.currentUsers }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">到期时间：</span>
              <span class="info-value" :class="{ 'expired': isLicenseExpired }">
                {{ licenseInfo.expiryDate }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">许可证状态：</span>
              <el-tag :type="licenseInfo.status === 'valid' ? 'success' : 'danger'">
                {{ licenseInfo.status === 'valid' ? '有效' : '无效' }}
              </el-tag>
            </div>
          </div>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="handleUpdateLicense">
              更新许可证
            </el-button>
            <el-button size="small" @click="handleCheckLicense">
              检查许可证
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '../store'
import { getSystemSettings, updateSystemSettings } from '../mock/api'

const systemStore = useSystemStore()

// 响应式数据
const loading = ref(false)

// 配置数据
const serverConfig = ref({
  serverIP: '***********00',
  serverPort: 8080,
  databaseUrl: '*****************************************',
  maxConnections: 100,
  connectionTimeout: 30
})

const runtimeConfig = ref({
  realtimeCacheSize: 200,
  deployCacheSize: 200,
  autoSelectGroupDeploy: true,
  maxConcurrentDeploy: 10,
  deployTimeout: 300
})

const storageConfig = ref({
  imagePath: 'D:\\HaiguangDesk\\Images',
  snapshotPath: 'D:\\HaiguangDesk\\Snapshots',
  tempPath: 'D:\\HaiguangDesk\\Temp',
  logPath: 'D:\\HaiguangDesk\\Logs',
  autoCleanTemp: true,
  cleanInterval: 7,
  maxLogSize: 100
})

const services = ref([
  {
    name: '部署服务',
    description: '负责系统镜像和快照的部署',
    status: 'running'
  },
  {
    name: '监控服务',
    description: '监控终端状态和系统性能',
    status: 'running'
  },
  {
    name: '数据库服务',
    description: '数据存储和管理服务',
    status: 'running'
  },
  {
    name: '网络服务',
    description: '网络通信和数据传输服务',
    status: 'stopped'
  }
])

const systemInfo = ref({
  version: '1.0.0',
  serverOS: 'Windows Server 2019',
  cpuUsage: 25,
  memoryUsage: 45,
  diskUsage: 60,
  uptime: '15天 8小时 32分钟',
  currentConnections: 45,
  lastUpdate: '2024-01-15 14:30:25'
})

const licenseInfo = ref({
  type: '企业版',
  maxUsers: 500,
  currentUsers: 45,
  expiryDate: '2025-12-31',
  status: 'valid'
})

// 计算属性
const isLicenseExpired = computed(() => {
  const expiryDate = new Date(licenseInfo.value.expiryDate)
  return expiryDate < new Date()
})

// 方法
const handleSaveServerConfig = async () => {
  try {
    await updateSystemSettings('server', serverConfig.value)
    ElMessage.success('服务器配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleTestConnection = () => {
  ElMessage.info('正在测试连接...')
  setTimeout(() => {
    ElMessage.success('连接测试成功')
  }, 2000)
}

const handleApplyRuntimeSettings = async () => {
  try {
    await updateSystemSettings('runtime', runtimeConfig.value)
    ElMessage.success('运行参数配置已应用')
  } catch (error) {
    ElMessage.error('应用设置失败')
  }
}

const handleRestoreRuntimeDefaults = () => {
  runtimeConfig.value = {
    realtimeCacheSize: 200,
    deployCacheSize: 200,
    autoSelectGroupDeploy: true,
    maxConcurrentDeploy: 10,
    deployTimeout: 300
  }
  ElMessage.success('已恢复默认参数')
}

const handleStartService = (service) => {
  service.status = 'running'
  ElMessage.success(`${service.name}启动成功`)
}

const handleStopService = (service) => {
  service.status = 'stopped'
  ElMessage.success(`${service.name}已停止`)
}

const handleRestartService = (service) => {
  ElMessage.info(`正在重启${service.name}...`)
  setTimeout(() => {
    service.status = 'running'
    ElMessage.success(`${service.name}重启成功`)
  }, 2000)
}

const handleSaveStorageConfig = async () => {
  try {
    await updateSystemSettings('storage', storageConfig.value)
    ElMessage.success('存储配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleBrowseImagePath = () => {
  ElMessage.info('浏览文件夹功能（需要桌面应用支持）')
}

const handleBrowseSnapshotPath = () => {
  ElMessage.info('浏览文件夹功能（需要桌面应用支持）')
}

const handleBrowseTempPath = () => {
  ElMessage.info('浏览文件夹功能（需要桌面应用支持）')
}

const handleBrowseLogPath = () => {
  ElMessage.info('浏览文件夹功能（需要桌面应用支持）')
}

const handleCheckDiskSpace = () => {
  ElMessage.info('正在检查磁盘空间...')
  setTimeout(() => {
    ElMessage.success('磁盘空间检查完成，剩余空间充足')
  }, 1500)
}

const handleRefreshSystemInfo = () => {
  ElMessage.info('正在刷新系统信息...')
  setTimeout(() => {
    systemInfo.value.lastUpdate = new Date().toLocaleString()
    systemInfo.value.cpuUsage = Math.floor(Math.random() * 50) + 10
    systemInfo.value.memoryUsage = Math.floor(Math.random() * 40) + 30
    ElMessage.success('系统信息已刷新')
  }, 1000)
}

const handleExportSystemInfo = () => {
  ElMessage.success('系统报告导出成功')
}

const handleUpdateLicense = () => {
  ElMessage.info('许可证更新功能')
}

const handleCheckLicense = () => {
  ElMessage.info('正在检查许可证...')
  setTimeout(() => {
    ElMessage.success('许可证检查完成，状态正常')
  }, 1500)
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const settings = await getSystemSettings()
    if (settings.server) Object.assign(serverConfig.value, settings.server)
    if (settings.runtime) Object.assign(runtimeConfig.value, settings.runtime)
    if (settings.storage) Object.assign(storageConfig.value, settings.storage)
  } catch (error) {
    ElMessage.error('加载系统设置失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.system-settings {
  background: #f5f5f5;
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.setting-card {
  height: fit-content;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #303133;
}

.card-content {
  padding-top: 10px;
}

.card-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.config-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.service-description {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.system-info,
.license-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.info-value.expired {
  color: #f56c6c;
}
</style>
