<template>
  <div class="disk-management">
    <el-container>
      <!-- 左侧树形目录 -->
      <el-aside width="300px" class="tree-aside">
        <div class="tree-header">
          <h4>磁盘目录</h4>
          <el-button size="small" @click="handleEditDirectory" :type="editMode ? 'success' : 'primary'">
            {{ editMode ? '完成编辑' : '编辑目录' }}
          </el-button>
        </div>
        <el-tree
          :data="diskTree"
          :props="treeProps"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          class="directory-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">{{ node.label }}</span>
              <div v-if="editMode" class="node-actions">
                <el-button size="small" @click.stop="handleAddDirectory(data)">+</el-button>
                <el-button size="small" @click.stop="handleDeleteDirectory(data)">-</el-button>
                <el-button size="small" @click.stop="handleRenameDirectory(data)">重命名</el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </el-aside>

      <!-- 右侧内容区 -->
      <el-main class="main-content">
        <!-- 磁盘列表视图 -->
        <div v-if="currentView === 'disks'">
          <div class="toolbar">
            <div class="toolbar-left">
              <el-button type="primary" @click="handleCreateDisk">
                <el-icon><Plus /></el-icon>
                新建磁盘
              </el-button>
              <el-button @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="toolbar-right">
              <el-input
                v-model="searchText"
                placeholder="请输入磁盘名称搜索"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <el-table
            :data="filteredDisks"
            v-loading="loading"
            stripe
            style="width: 100%"
            class="data-table"
          >
            <el-table-column prop="name" label="磁盘名称" min-width="150" />
            <el-table-column prop="size" label="大小(G)" width="100" />
            <el-table-column prop="showSystemMenu" label="显示系统选单" width="120">
              <template #default="{ row }">
                <el-tag :type="row.showSystemMenu ? 'success' : 'info'">
                  {{ row.showSystemMenu ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="showHelp" label="显示帮助" width="100">
              <template #default="{ row }">
                <el-tag :type="row.showHelp ? 'success' : 'info'">
                  {{ row.showHelp ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="delayShutdown" label="延迟关机" width="100">
              <template #default="{ row }">
                <el-tag :type="row.delayShutdown ? 'success' : 'info'">
                  {{ row.delayShutdown ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="delayTime" label="延迟关机等待时间" width="140" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleEditDisk(row)">
                  属性
                </el-button>
                <el-button size="small" type="danger" @click="handleDeleteDisk(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 操作系统列表视图 -->
        <div v-if="currentView === 'systems'">
          <div class="toolbar">
            <div class="toolbar-left">
              <el-button type="primary" @click="handleCreateSystem">
                <el-icon><Plus /></el-icon>
                创建操作系统
              </el-button>
              <el-button type="success" @click="handleCreatePartition">
                <el-icon><FolderAdd /></el-icon>
                创建分区
              </el-button>
              <el-button @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="toolbar-right">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>磁盘目录</el-breadcrumb-item>
                <el-breadcrumb-item>{{ currentDiskName }}</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
          </div>

          <el-table
            :data="operatingSystems"
            v-loading="loading"
            stripe
            style="width: 100%"
            class="data-table"
          >
            <el-table-column prop="name" label="操作系统名称" min-width="150" />
            <el-table-column prop="snapshotName" label="引用操作系统快照名称" min-width="180" />
            <el-table-column prop="showSystem" label="是否显示操作系统" width="140">
              <template #default="{ row }">
                <el-tag :type="row.showSystem ? 'success' : 'info'">
                  {{ row.showSystem ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="supportShare" label="是否支持分区共享" width="140">
              <template #default="{ row }">
                <el-tag :type="row.supportShare ? 'success' : 'info'">
                  {{ row.supportShare ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isDefault" label="是否默认操作系统" width="140">
              <template #default="{ row }">
                <el-tag :type="row.isDefault ? 'success' : 'info'">
                  {{ row.isDefault ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="waitTime" label="进入系统等待时间" width="140" />
            <el-table-column prop="rootImage" label="所引用快照的根分区镜像名称" min-width="200" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleEditSystem(row)">
                  属性
                </el-button>
                <el-button size="small" type="danger" @click="handleDeleteSystem(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>

    <!-- 新建磁盘对话框 -->
    <el-dialog
      v-model="createDiskDialogVisible"
      title="新建磁盘"
      width="600px"
      @close="resetCreateDiskForm"
    >
      <el-form :model="createDiskForm" :rules="createDiskRules" ref="createDiskFormRef" label-width="150px">
        <el-form-item label="磁盘名称" prop="name">
          <el-input v-model="createDiskForm.name" placeholder="请输入磁盘名称" />
        </el-form-item>
        <el-form-item label="磁盘大小(G)" prop="size">
          <el-input-number v-model="createDiskForm.size" :min="1" :max="10000" />
          <div class="form-tip">磁盘大小请设置为终端磁盘容量和</div>
        </el-form-item>
        <el-form-item label="是否显示帮助" prop="showHelp">
          <el-switch v-model="createDiskForm.showHelp" />
        </el-form-item>
        <el-form-item label="是否显示操作系统选单" prop="showSystemMenu">
          <el-switch v-model="createDiskForm.showSystemMenu" />
        </el-form-item>
        <el-form-item label="是否延迟关机" prop="delayShutdown">
          <el-switch v-model="createDiskForm.delayShutdown" />
        </el-form-item>
        <el-form-item label="延迟关机等待时间" prop="delayTime" v-if="createDiskForm.delayShutdown">
          <el-input-number v-model="createDiskForm.delayTime" :min="0" :max="300" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        <el-form-item label="保留磁盘空间大小(G)" prop="reservedSpace">
          <el-input-number v-model="createDiskForm.reservedSpace" :min="0" :max="1000" />
        </el-form-item>
        <el-form-item label="需要验证密码的管理操作" prop="passwordOperations">
          <el-checkbox-group v-model="createDiskForm.passwordOperations">
            <el-checkbox label="F1">F1 查看系统信息</el-checkbox>
            <el-checkbox label="F2">F2 修复系统索引</el-checkbox>
            <el-checkbox label="F3">F3 还原系统</el-checkbox>
            <el-checkbox label="F4">F4 保存系统</el-checkbox>
            <el-checkbox label="F5">F5 删除系统</el-checkbox>
            <el-checkbox label="F6">F6 卸载</el-checkbox>
            <el-checkbox label="F10">F10 显示隐藏系统</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDiskDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateDiskConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 创建操作系统对话框 -->
    <el-dialog
      v-model="createSystemDialogVisible"
      title="创建操作系统"
      width="700px"
      @close="resetCreateSystemForm"
    >
      <el-form :model="createSystemForm" :rules="createSystemRules" ref="createSystemFormRef" label-width="180px">
        <el-form-item label="操作系统名称" prop="name">
          <el-input v-model="createSystemForm.name" placeholder="请输入操作系统名称" />
        </el-form-item>
        <el-form-item label="引用系统快照" prop="snapshotId">
          <el-select v-model="createSystemForm.snapshotId" placeholder="请选择系统快照">
            <el-option label="Win10_Office_2019" value="1" />
            <el-option label="Win7_Dev_Tools" value="2" />
            <el-option label="Ubuntu_20_04_Server" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否默认操作系统" prop="isDefault">
          <el-switch v-model="createSystemForm.isDefault" />
        </el-form-item>
        <el-form-item label="默认等待时间（秒）" prop="waitTime">
          <el-input-number v-model="createSystemForm.waitTime" :min="0" :max="300" />
        </el-form-item>
        <el-form-item label="是否显示操作系统" prop="showSystem">
          <el-switch v-model="createSystemForm.showSystem" />
        </el-form-item>
        <el-form-item label="进入操作系统密码" prop="password">
          <el-input v-model="createSystemForm.password" type="password" placeholder="可选" />
        </el-form-item>
        <el-form-item label="是否支持分区共享" prop="supportShare">
          <el-switch v-model="createSystemForm.supportShare" />
        </el-form-item>
        <el-form-item label="是否支持Linux系统直接写入" prop="supportLinuxWrite">
          <el-switch v-model="createSystemForm.supportLinuxWrite" />
        </el-form-item>
        <el-form-item label="是否使用共享分区" prop="useSharedPartition">
          <el-switch v-model="createSystemForm.useSharedPartition" />
        </el-form-item>
        <el-form-item label="是否禁用光驱" prop="disableCDROM">
          <el-switch v-model="createSystemForm.disableCDROM" />
        </el-form-item>
        <el-form-item label="是否禁用USB存储设备" prop="disableUSB">
          <el-switch v-model="createSystemForm.disableUSB" />
        </el-form-item>
        <el-form-item label="是否启动前部署所有数据" prop="deployAllData">
          <el-switch v-model="createSystemForm.deployAllData" />
        </el-form-item>
        <el-form-item label="是否不执行还原优化" prop="noRestoreOptimization">
          <el-switch v-model="createSystemForm.noRestoreOptimization" />
        </el-form-item>
        <el-form-item label="是否磁盘刷新优化" prop="diskRefreshOptimization">
          <el-switch v-model="createSystemForm.diskRefreshOptimization" />
        </el-form-item>
        <el-form-item label="启动加载模式" prop="loadMode">
          <el-radio-group v-model="createSystemForm.loadMode">
            <el-radio label="default">默认</el-radio>
            <el-radio label="overwrite">覆盖PXE数据段</el-radio>
            <el-radio label="highMemory">可用内存高端</el-radio>
            <el-radio label="nearPXE">临近PXE数据段</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启动部署模式" prop="deployMode">
          <el-radio-group v-model="createSystemForm.deployMode">
            <el-radio label="noDeploy">不预部署</el-radio>
            <el-radio label="deployAndClosePXE">预部署后关闭PXE</el-radio>
            <el-radio label="deployKeepPXE">预部署后不关闭PXE</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createSystemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateSystemConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 创建分区对话框 -->
    <el-dialog
      v-model="createPartitionDialogVisible"
      title="创建分区"
      width="600px"
      @close="resetCreatePartitionForm"
    >
      <el-form :model="createPartitionForm" :rules="createPartitionRules" ref="createPartitionFormRef" label-width="150px">
        <el-form-item label="分区名称" prop="name">
          <el-input v-model="createPartitionForm.name" placeholder="请输入分区名称" />
        </el-form-item>
        <el-form-item label="文件系统类型" prop="fileSystem">
          <el-select v-model="createPartitionForm.fileSystem" placeholder="请选择文件系统类型">
            <el-option label="NTFS" value="NTFS" />
            <el-option label="FAT32" value="FAT32" />
            <el-option label="EXT4" value="EXT4" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择数据分区镜像" prop="dataImage">
          <el-select v-model="createPartitionForm.dataImage" placeholder="请选择数据分区镜像">
            <el-option label="Office_2019_D" value="Office_2019_D" />
            <el-option label="Dev_Tools_D" value="Dev_Tools_D" />
            <el-option label="Common_Software_D" value="Common_Software_D" />
          </el-select>
        </el-form-item>
        <el-form-item label="分区大小(G)" prop="size">
          <el-input-number v-model="createPartitionForm.size" :min="1" :max="1000" />
        </el-form-item>
        <el-form-item label="分区还原方式" prop="restoreMode">
          <el-radio-group v-model="createPartitionForm.restoreMode">
            <el-radio label="always">每次还原</el-radio>
            <el-radio label="manual">手动还原</el-radio>
            <el-radio label="daily">每天第一次启动还原</el-radio>
            <el-radio label="weekly">每周第一次启动还原</el-radio>
            <el-radio label="monthly">每月第一次启动还原</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="该分区是否共享" prop="isShared">
          <el-switch v-model="createPartitionForm.isShared" :disabled="!supportPartitionShare" />
          <div class="form-tip" v-if="!supportPartitionShare">
            当该分区所属的操作系统支持分区共享后才可以选择
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createPartitionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreatePartitionConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDiskStore } from '../store'
import { getDiskTree, getDisks, getOperatingSystems, createDisk, createOperatingSystem, createPartition } from '../mock/api'

const diskStore = useDiskStore()

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const editMode = ref(false)
const currentView = ref('disks') // 'disks' | 'systems'
const currentDiskId = ref('')
const currentDiskName = ref('')

// 对话框显示状态
const createDiskDialogVisible = ref(false)
const createSystemDialogVisible = ref(false)
const createPartitionDialogVisible = ref(false)

// 表单引用
const createDiskFormRef = ref()
const createSystemFormRef = ref()
const createPartitionFormRef = ref()

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 表单数据
const createDiskForm = ref({
  name: '',
  size: 100,
  showHelp: false,
  showSystemMenu: true,
  delayShutdown: false,
  delayTime: 30,
  reservedSpace: 10,
  passwordOperations: []
})

const createSystemForm = ref({
  name: '',
  snapshotId: '',
  isDefault: false,
  waitTime: 10,
  showSystem: true,
  password: '',
  supportShare: false,
  supportLinuxWrite: false,
  useSharedPartition: false,
  disableCDROM: false,
  disableUSB: false,
  deployAllData: false,
  noRestoreOptimization: false,
  diskRefreshOptimization: false,
  loadMode: 'default',
  deployMode: 'noDeploy'
})

const createPartitionForm = ref({
  name: '',
  fileSystem: 'NTFS',
  dataImage: '',
  size: 50,
  restoreMode: 'always',
  isShared: false
})

// 表单验证规则
const createDiskRules = {
  name: [{ required: true, message: '请输入磁盘名称', trigger: 'blur' }],
  size: [{ required: true, message: '请输入磁盘大小', trigger: 'blur' }]
}

const createSystemRules = {
  name: [{ required: true, message: '请输入操作系统名称', trigger: 'blur' }],
  snapshotId: [{ required: true, message: '请选择系统快照', trigger: 'change' }]
}

const createPartitionRules = {
  name: [{ required: true, message: '请输入分区名称', trigger: 'blur' }],
  fileSystem: [{ required: true, message: '请选择文件系统类型', trigger: 'change' }],
  dataImage: [{ required: true, message: '请选择数据分区镜像', trigger: 'change' }]
}

// 计算属性
const diskTree = computed(() => diskStore.diskTree)
const filteredDisks = computed(() => {
  if (!searchText.value) {
    return diskStore.disks
  }
  return diskStore.disks.filter(disk =>
    disk.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const operatingSystems = computed(() => diskStore.operatingSystems)

const supportPartitionShare = computed(() => {
  // 这里应该根据当前选择的操作系统来判断是否支持分区共享
  return true
})

// 方法
const handleNodeClick = async (data) => {
  if (data.type === 'directory') {
    // 点击目录，显示该目录下的磁盘列表
    currentView.value = 'disks'
    loading.value = true
    try {
      const disks = await getDisks(data.id)
      diskStore.setDisks(disks)
    } catch (error) {
      ElMessage.error('加载磁盘列表失败')
    } finally {
      loading.value = false
    }
  } else if (data.type === 'disk') {
    // 点击磁盘，显示该磁盘下的操作系统列表
    currentView.value = 'systems'
    currentDiskId.value = data.id
    currentDiskName.value = data.name
    loading.value = true
    try {
      const systems = await getOperatingSystems(data.id)
      diskStore.setOperatingSystems(systems)
    } catch (error) {
      ElMessage.error('加载操作系统列表失败')
    } finally {
      loading.value = false
    }
  }
}

const handleEditDirectory = () => {
  editMode.value = !editMode.value
}

const handleAddDirectory = (data) => {
  ElMessage.info('新建目录功能')
}

const handleDeleteDirectory = (data) => {
  ElMessage.info('删除目录功能')
}

const handleRenameDirectory = (data) => {
  ElMessage.info('重命名目录功能')
}

const handleRefresh = async () => {
  loading.value = true
  try {
    if (currentView.value === 'disks') {
      const disks = await getDisks()
      diskStore.setDisks(disks)
    } else {
      const systems = await getOperatingSystems(currentDiskId.value)
      diskStore.setOperatingSystems(systems)
    }
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleCreateDisk = () => {
  createDiskDialogVisible.value = true
}

const handleCreateDiskConfirm = async () => {
  if (!createDiskFormRef.value) return
  
  try {
    await createDiskFormRef.value.validate()
    const newDisk = await createDisk(createDiskForm.value)
    diskStore.disks.push(newDisk)
    createDiskDialogVisible.value = false
    ElMessage.success('磁盘创建成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
    }
  }
}

const handleEditDisk = (row) => {
  ElMessage.info('编辑磁盘属性功能')
}

const handleDeleteDisk = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除磁盘"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleCreateSystem = () => {
  createSystemDialogVisible.value = true
}

const handleCreateSystemConfirm = async () => {
  if (!createSystemFormRef.value) return
  
  try {
    await createSystemFormRef.value.validate()
    const newSystem = await createOperatingSystem(currentDiskId.value, createSystemForm.value)
    diskStore.operatingSystems.push(newSystem)
    createSystemDialogVisible.value = false
    ElMessage.success('操作系统创建成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
    }
  }
}

const handleEditSystem = (row) => {
  ElMessage.info('编辑操作系统属性功能')
}

const handleDeleteSystem = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除操作系统"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleCreatePartition = () => {
  createPartitionDialogVisible.value = true
}

const handleCreatePartitionConfirm = async () => {
  if (!createPartitionFormRef.value) return
  
  try {
    await createPartitionFormRef.value.validate()
    const newPartition = await createPartition(currentDiskId.value, createPartitionForm.value)
    createPartitionDialogVisible.value = false
    ElMessage.success('分区创建成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
    }
  }
}

const resetCreateDiskForm = () => {
  createDiskForm.value = {
    name: '',
    size: 100,
    showHelp: false,
    showSystemMenu: true,
    delayShutdown: false,
    delayTime: 30,
    reservedSpace: 10,
    passwordOperations: []
  }
  if (createDiskFormRef.value) {
    createDiskFormRef.value.resetFields()
  }
}

const resetCreateSystemForm = () => {
  createSystemForm.value = {
    name: '',
    snapshotId: '',
    isDefault: false,
    waitTime: 10,
    showSystem: true,
    password: '',
    supportShare: false,
    supportLinuxWrite: false,
    useSharedPartition: false,
    disableCDROM: false,
    disableUSB: false,
    deployAllData: false,
    noRestoreOptimization: false,
    diskRefreshOptimization: false,
    loadMode: 'default',
    deployMode: 'noDeploy'
  }
  if (createSystemFormRef.value) {
    createSystemFormRef.value.resetFields()
  }
}

const resetCreatePartitionForm = () => {
  createPartitionForm.value = {
    name: '',
    fileSystem: 'NTFS',
    dataImage: '',
    size: 50,
    restoreMode: 'always',
    isShared: false
  }
  if (createPartitionFormRef.value) {
    createPartitionFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    const tree = await getDiskTree()
    diskStore.setDiskTree(tree)
  } catch (error) {
    ElMessage.error('加载磁盘目录失败')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.disk-management {
  background: white;
  border-radius: 4px;
  height: calc(100vh - 120px);
}

.tree-aside {
  border-right: 1px solid #e4e7ed;
  padding: 20px;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tree-header h4 {
  margin: 0;
}

.directory-tree {
  background: transparent;
  border: none;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
}

.node-actions {
  display: flex;
  gap: 5px;
}

.main-content {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.data-table {
  border-radius: 4px;
  overflow: hidden;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
