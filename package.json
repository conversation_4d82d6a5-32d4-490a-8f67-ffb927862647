{"name": "haiguang-desk-web", "version": "1.0.0", "description": "海光Desk系统管理版Web前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "mockjs": "^1.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "unplugin-vue-components": "^0.25.0", "unplugin-auto-import": "^0.16.0"}}