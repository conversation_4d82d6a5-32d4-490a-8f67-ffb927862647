


海光Desk系统管理版操作说明手册




版本：0.01






目录
1. 海光Desk系统管理版简介	3
2. 镜像管理	3
2.1 镜像管理内容	3
2.2 制作镜像	4
2.3 镜像删除	4
2.4 镜像属性设置	4
/**2.5 多系统镜像制作（此功能忽略）	4
2.6 刷新操作	5
2.7 个性化显示	5
2.8 镜像搜索	5
3. 系统快照管理	5
3.1 新建操作系统快照	5
3.2 复制操作系统快照	6
3.4 合并操作系统快照	6
3.5 编辑合并后的操作系统快照 	6
3.6 编辑操作系统快照属性	6
3.7 刷新操作	6
3.8 个性化显示	6
3.9 系统快照搜索	7
4. 磁盘管理	7
4.1 磁盘管理界面	7
4.2 编辑磁盘目录	7
4.4 新建磁盘	8
4.5 编辑磁盘	8
4.6 创建操作系统	8
4.7 编辑操作系统属性	8
4.8 创建分区	9
5. 分组管理	9
5.1 分组管理界面	9
5.2 编辑分组目录	9
5.3 拖拽移动分组目录	10
5.2 新建分组	10
5.4 编辑分组	10
6 终端管理	11
6.2 添加占位机	11
6.4 全组重启	11
6.6 单个终端关机/重启/唤醒/删除	12
6.7 编辑终端属性	12
6.8 批量操作	12
7. 课程管理	12
7.1 课程管理界面	12
7.2 新建课程	13
7.3 快速新建课程	13
7.4 编辑课程	13
7.5自定义课表	13
7.6 切换列表	13
8. 系统设置	14
8.1 系统设置界面	14
8.2 运行参数配置	14
9. 总结	14



































1. 海光Desk系统管理版简介
海光Desk系统管理版是利用强大的硬盘虚拟技术和P2P部署技术，针对大规模计算机的系统自动部署、集中统一管理等量身定制的一款部署软件。采用分组和虚拟硬盘模板的管理策略，用一台服务器对所有机房计算机进行集中管理和统一维护。通过一台服务器实现对几十台至几千台计算机终端的管理，可以快速构建一个高效、安全、统一的计算机教学实验管理平台。

海光Desk系统管理版具有强大快速的自动部署能力，系统完全部署后，终端计算机可以断网离线或脱离服务器运行，实现“在线管理、离线运行”，最大限度满足客户需要。支持“跨网段跨路由”运行，单组最多支持254台终端计算机，最大限度适用用户的现状和需求。

海光Desk系统管理版具有“终端集中管理、桌面智能部署、桌面自动更新、断网离线使用”的特色。
2. 镜像管理
镜像管理是海光Desk系统管理版的核心功能模块，负责管理客户端系统分区和数据分区的镜像文件。制作完成的镜像相当于一个模板，操作系统创建时可以引用这些镜像。镜像管理中存放的是镜像模板，相同作用的镜像无需重复制作。镜像需要通过客户端制作上传，存储在服务器数据库中，根据实际需要配置到相应的磁盘。
2.1 镜像管理内容
点击侧边栏“镜像管理”，进入镜像管理界面，可查看以列表形式呈现的当前系统中已上传的镜像的所有记录。路径：侧边栏→镜像管理。

列表上方有操作工具栏，包含"新建分区镜像"、"刷新"、”编辑”按键，右侧有搜索框用于快速查找镜像。

列表显示镜像名称、镜像类型、镜像大小、状态（上传中、已完成）、创建时间、描述等信息以及操作栏目，操作栏目中有“属性”、“删除”按键。
2.2 制作镜像
进入客户端的操作系统，打开海光Desk系统管理版控制台，点击"新建分区镜像"，在弹出的新建分区镜像表单中，选择要上传的分区，输入分区镜像名称、分区镜像类型后，点击"确定"，开始上传镜像。分区镜像类型要与当前选择上传的分区相匹配，镜像名称可自由命名，如Win7 64 C、Win7 64 D等。上传完成后，在镜像状态处会显示"已完成"。路径：镜像管理→新建分区镜像。

可分别对系统分区和数据分区进行镜像制作，制作的镜像是一个镜像模板，操作系统创建时会引用镜像模板，相同镜像可以不用重复上传。镜像的制作需要在客户端的机器上进行，确保镜像内容的完整性和准确性。

2.3 镜像删除
当镜像不再需要时，可以点击操作栏目中的“删除”按键，系统会弹出确认提示框，确认后即可删除该镜像。需要注意的是，镜像删除后不可恢复，因此在删除前请确认该镜像确实不再需要使用。路径：镜像管理→操作→删除。

删除镜像前应确保该镜像未被任何操作系统快照引用，否则可能影响相关系统的正常运行。建议在删除前先检查镜像的使用情况，避免误删除重要镜像。

2.4 镜像属性设置
点击操作栏目中的“属性”按键，当前记录的变为可修改状态，可直接镜像重命名、修改描述等操作。路径：镜像管理→操作→属性。

合理的镜像命名和描述有助于后续的管理和维护工作，建议采用统一的命名规范，包含操作系统版本、分区类型、软件环境等关键信息。

/**2.5 多系统镜像制作（此功能忽略）
多系统分区镜像制作是指安装多个操作系统并分别进行分区镜像制作，可以通过两种方式实现。第一种方式是在同一台机器上安装不同的操作系统，在第一个系统的分区镜像上传完成后，重启到系统选单界面按F6选择快速卸载，然后安装第二个操作系统再制作分区镜像上传，多个系统分区镜像制作都按照此方法进行。第二种方式是在不同的机器上安装不同的操作系统，每台机器安装一个操作系统，然后各个机器分别制作分区镜像即可。路径：镜像管理→新建分区镜像。

多系统镜像制作能够满足不同教学和办公需求，实现一机多用的目标。在制作过程中要注意保持镜像的独立性和完整性，确保每个系统镜像都能正常使用。**/

2.6 刷新操作
点击“刷新”按键，该页面将刷新以获取最新数据，刷新成功后显示“刷新成功”。

2.7 个性化显示
点击 “编辑” 按键，随即弹出编辑表单。编辑表单内以列表形式呈现本页面所有栏目，每个栏目前均设有勾选框。按需点击栏目前的勾选框（支持多选），被勾选的栏目将实时在页面上显示。

2.8 镜像搜索
在输入框中输入目标镜像名称后，系统将基于输入内容对列表项进行筛选，仅返回名称字段中完整且连续包含该目标字符串的记录。

3. 系统快照管理
点击侧边栏“系统快照管理”，进入系统快照管理界面，可查看以列表形式呈现的当前系统中已创建的系统快照管理的所有记录。路径：侧边栏→系统快照管理。

列表上方有操作工具栏，包含"新建系统快照"、”合并系统快照”、"刷新"、“编辑”按键，右侧有搜索框用于快速查找镜像。

列表显示系统快照名称、系统快照类型、系统分区镜像、数据分区镜像、创建时间、描述等信息以及操作栏目，操作栏目中有“属性”、“复制”、“删除”按键。

3.1 新建操作系统快照
点击工具栏中的"新建系统快照"按键，打开新建系统快照表单。输入系统快照名称，选择系统分区镜像和数据分区镜像。点击确定后，提示“系统快照创建成功”。路径：磁盘管理→操作系统快照管理→新建系统快照。

3.2 复制操作系统快照
点击操作栏目中的“复制”按键，打开复制系统快照管理表单，输入重命名后，点击确认即可复制该快照。路径：系统快照管理→操作→复制。

3.3 删除操作系统快照
当操作系统快照不再需要时，可以点击操作栏目中的“删除”按键，系统会弹出确认提示框，确认后即可删除该镜像。需要注意的是，系统快照删除后不可恢复，因此在删除前请确认该系统快照确实不再需要使用。路径：系统快照管理→操作→删除。

删除镜像前应确保该镜像未被任何操作系统快照引用，否则可能影响相关系统的正常运行。建议在删除前先检查镜像的使用情况，避免误删除重要镜像。

3.4 合并操作系统快照
点击工具栏中的"合并系统快照"按键，打开合并系统快照表单。输入合并后的系统快照名称，选择需要合并的系统快照（以列表形式展示当前所有快照，每个快照前面有一个复选框，点击即可勾选快照，勾选的快照数量需要大于等于2）。点击确定后，提示“系统快照合并成功”。合并后的快照在系统快照列表中以父子级结构呈现，自动隐藏子级（合并之前的节点），点击展开角标后会出现该合并快照的所有子快照。路径：磁盘管理→操作系统快照管理→合并系统快照。

3.5 编辑合并后的操作系统快照
合并快照下的每一条子快照记录依然具备系统快照名称、系统快照类型、系统分区镜像、数据分区镜像、创建时间、描述等信息，操作栏目中有“属性”、“复制”、“删除”按键。可选择操作栏目的按键完成相应的操作。

3.6 编辑操作系统快照属性
点击操作栏目中的“属性”按键，打开操作系统快照属性表单，在表单中可编辑系统快照名称、描述等信息，展示该快照所包含的数据分区，可通过拖动调整数据分区的顺序，或者通过点击每条分区记录右侧的↑/↓键调整分区位置。路径：镜像管理→操作→属性。

3.7 刷新操作
点击“刷新”按键，该页面将刷新以获取最新数据，刷新成功后显示“刷新成功”。

3.8 个性化显示
点击 “编辑” 按键，随即弹出编辑表单。编辑表单内以列表形式呈现本页面所有栏目，每个栏目前均设有勾选框。按需点击栏目前的勾选框（支持多选），被勾选的栏目将实时在页面上显示。

3.9 系统快照搜索
在输入框中输入目标系统快照名称后，系统将基于输入内容对列表项进行筛选，仅返回名称字段中完整且连续包含该目标字符串的记录。

4. 磁盘管理
磁盘管理模块包含磁盘管理和系统快照管理两个主要功能，磁盘相当于一个虚拟的硬盘，系统快照可被操作系统引用。海光Desk系统管理版采用虚拟技术，在服务器上创建虚拟磁盘，实现对所有客户端硬盘的集中统一管理。虚拟磁盘具有同一磁盘可以安装多个操作系统、同一磁盘可被多个分组引用、磁盘剩余空间智能调配、虚拟磁盘个数不受限制等特点，为系统管理提供了极大的灵活性。

4.1 磁盘管理界面
点击侧边栏"磁盘管理"，进入磁盘管理界面。路径：侧边栏→磁盘管理。

界面左侧采用树形列表结构，展示磁盘目录的层级关系，点击展开/收缩目录，目录右上角为“编辑目录”按键。

当点击磁盘目录时，界面右侧列表显示该目录下磁盘名称、大小(G)、显示系统选单、显示帮助、延迟关机、延迟关机等待时间等信息栏以及操作栏目，操作栏目中有“属性”、“删除”按键。列表上方的操作工具栏为"新建磁盘"、"刷新"按键，右侧有搜索框用于快速查找磁盘。

当点击磁盘目录中的磁盘名称时，界面右侧列表显示操作系统名称、引用操作系统快照名称、是否显示操作系统、是否支持分区共享、是否默认操作系统、进入系统等待时间、所引用快照的根分区镜像名称等信息栏以及操作栏目，操作栏目中有“属性”、“删除”按键。列表上方的操作工具栏为"创建操作系统"、“创建分区”、"刷新"按键。

4.2 编辑磁盘目录
点击“编辑目录”按键，每一级的目录右侧出现“+”、“-”、“重命名”按键。点击“+”在当前层级下新建目录，点击“-”删除当前目录，点击“重命名”按键对当前目录进行重命名。路径：磁盘管理→磁盘目录→编辑目录。

4.3 拖拽移动磁盘目录
拖拽磁盘目录，可快速移动磁盘的位置。路径：磁盘目录→拖拽移动磁盘目录。

4.4 新建磁盘
点击工具栏中的"新建磁盘"按键，弹出新建磁盘表单。在表单中设置磁盘相关参数：磁盘名称、磁盘大小(G)、是否显示帮助、是否显示操作系统选单、是否延迟关机、延迟关机等待时间、保留磁盘空间大小(G)、选择需要验证密码的管理操作（支持多选：F1 查看系统信息、F2 修复系统索引、F3 还原系统、F4 保存系统、F5 删除系统、F6 卸载、F10 显示隐藏系统）。设置完成后点击"确定"按键创建磁盘。路径：磁盘管理→点击目录→新建磁盘。

磁盘大小的文字提示：磁盘大小请设置为终端磁盘容量和。

4.5 编辑磁盘
点击工具栏中的"属性"按键，弹出编辑磁盘表单。在表单中可以编辑磁盘相关参数：磁盘名称、磁盘大小(G)、是否显示帮助、是否显示操作系统选单、是否延迟关机、延迟关机等待时间、保留磁盘空间大小(G)、选择需要验证密码的管理操作（支持多选：F1 查看系统信息、F2 修复系统索引、F3 还原系统、F4 保存系统、F5 删除系统、F6 卸载、F10 显示隐藏系统）。设置完成后点击"确定"按键创建磁盘。路径：磁盘管理→点击目录→属性。

4.6 创建操作系统
点击"创建操作系统"按键，弹出创建操作系统表单，在表单中设置操作系统相关参数：操作系统名称、引用系统快照（从下拉列表中单选目标快照）、是否默认操作系统、默认等待时间（秒）、是否显示操作系统、进入操作系统密码、是否支持分区共享、是否支持Linux系统直接写入、是否使用共享分区、是否禁用光驱、是否禁用USB存储设备、是否启动前部署所有数据、是否不执行还原优化、是否磁盘刷新优化，启动加载模式（单选：默认、覆盖PXE数据段、可用内存高端、临近PXE数据段）、启动部署模式（不预部署、预部署后关闭PXE、预部署后不关闭PXE），设置完成后点击"确定"按键创建操作系统。路径：磁盘管理→选择磁盘→创建操作系统。

创建操作系统是将系统快照实际部署到虚拟磁盘的过程，完成后客户端就可以使用该操作系统。在创建过程中要确保选择正确的系统快照，避免部署错误的系统环境。

4.7 编辑操作系统属性
在操作系统行点击"属性"按键，打开操作系统属性设置表单，在表单中设置操作系统相关参数：操作系统名称、引用系统快照（从下拉列表中单选目标快照）、是否默认操作系统、默认等待时间（秒）、是否显示操作系统、进入操作系统密码、是否支持分区共享、是否支持Linux系统直接写入、是否使用共享分区、是否禁用光驱、是否禁用USB存储设备、是否启动前部署所有数据、是否不执行还原优化、是否磁盘刷新优化，启动加载模式（单选：默认、覆盖PXE数据段、可用内存高端、临近PXE数据段）、启动部署模式（不预部署、预部署后关闭PXE、预部署后不关闭PXE），设置完成后点击"确定"按键。路径：磁盘管理→选择磁盘→选择操作系统→属性。

操作系统属性设置对于系统的使用体验和安全性具有重要影响，应根据实际需求进行合理配置。如果镜像被引用后客户端系统出现蓝屏、重启或其他故障，可以通过调试启动加载模式和部署模式来解决问题。

4.8 创建分区
点击"创建分区"按键，弹出创建分区表单，在表单中设置分区相关参数：分区名称、文件系统类型、选择数据分区镜像（单选）、分区大小(G)、分区还原方式（单选：每次还原、手动还原、每天第一次启动还原、每周第一次启动还原、每月第一次启动还原）、该分区是否共享（默认不可选，当该分区所属的操作系统支持分区共享后才可以选择），设置完成后点击"确定"按键。路径：磁盘管理→选择磁盘→创建分区。

分区的还原方式和共享方式修改后，客户端重启即可生效。合理设置分区属性可以有效保护系统安全，防止恶意修改和数据丢失。

5. 分组管理
分组管理是海光Desk系统管理版集中管理的重要方式，可以按不同的教室、不同的年级或其他分组类型对用户机器进行多个分组管理。每组最大可管理终端数量为254个，支持跨网段跨路由运行，满足各种网络环境的需求。

5.1 分组管理界面
点击侧边栏"分组管理"，进入分组管理界面。路径：侧边栏→分组管理。

界面左侧采用树形列表结构，展示分组目录的层级关系，点击展开/收缩目录，目录右上角为“编辑目录”按键。

当点击分组目录时，界面右侧采用卡片式布局展示该目录下所有分组的信息，每个分组以独立卡片形式显示，包含分组名称、终端数量、在线数量、部署状态、磁盘信息、IP范围、创建时间等关键信息。

每个分组卡片右上角有"编辑分组"按键，每个卡片底部有一排操作按键，包含"开始部署"、"停止部署"、"删除分组"等。点击卡片进入相应的终端管理界面。

卡片界面上方有操作工具栏，包含"新建分组"、"刷新"按键，右侧有搜索框用于快速查找分组。

当点击分组目录中的目标分组时，进入终端管理界面。

5.2 编辑分组目录
点击“编辑目录”按键，每一级的目录右侧出现“+”、“-”、“重命名”按键。点击“+”在当前层级下新建目录，点击“-”删除当前目录，点击“重命名”按键对当前目录进行重命名。路径：分组管理→分组目录→编辑目录。

5.3 拖拽移动分组目录
拖拽分组目录，可快速移动分组的位置。路径：分组目录→拖拽移动分组目录。

5.2 新建分组
点击工具栏中的"新建分组"按键，弹出新建分组表单。在新建分组表单中设置分组相关参数：分组名称、分组名称、选择磁盘、起始IP地址、子网掩码、分组网关、首选DNS服务器、备用DNS服务器、机器名前缀、机器名起始编号、是否默认组、开机自动后台部署、执行软件预注册、系统部署策略等信息，以及高级按键。路径：分组管理→新建分组。

点击“高级”按键进入高级部署表单，表单信息包括：组内机器部署窗口大小、组内机器种子线程数、部署数据缓冲区大小、部署二级缓冲区大小、磁盘IO缓冲区开始地址、磁盘IO缓冲区大小、最低响应时间、是否按课表要求部署、压缩存储、启动DHCP代理、是否使用第三方DHCP服务器、是否可以禁用网卡、代理服务器IP地址、代理服务器端口、显示机器名称、显示正在使用系统名称、显示机器MAC地址、显示机器IP地址、显示提示信息、显示器分辨率宽、显示器分辨率高、显示器刷新频率、缩放比例百分比、开机屏幕复制、开机屏幕扩展、开机恢复图标、开机等待时间、恢复默认设置。路径：分组管理→新建分组→高级。

首次创建分组时应将默认组选项打开，组内终端都连接完成后可以关闭默认组。分组属性中的高级设置参数按照默认值即可，如果修改后导致部署异常，可以单击恢复默认设置。

需要创建多个分组时，可以重复新建分组的过程，但要注意组名不能重复。终端机器第一次和服务器连接时，需要将该终端所在的分组设置为默认组，该组内的终端都连接完成后，可以将默认组关闭。当存在多个分组时，如果有终端机器加入组中，需要将该终端机器所在的组设置为默认。

组内终端使用的系统必须在分组属性的磁盘选择处选择和组内机器匹配的磁盘。多分组管理能够实现不同区域、不同用途的终端分类管理，提高管理效率和系统性能。

5.4 编辑分组
点击“编辑分组”按键，打开分组编辑表单。在编辑分组表单中设置分组相关参数：分组名称、分组名称、选择磁盘、起始IP地址、子网掩码、分组网关、首选DNS服务器、备用DNS服务器、机器名前缀、机器名起始编号、是否默认组、开机自动后台部署、执行软件预注册、系统部署策略等信息，以及高级按键。路径：分组管理→选择分组→编辑分组。

点击“高级”按键进入高级部署表单，表单信息包括：组内机器部署窗口大小、组内机器种子线程数、部署数据缓冲区大小、部署二级缓冲区大小、磁盘IO缓冲区开始地址、磁盘IO缓冲区大小、最低响应时间、是否按课表要求部署、压缩存储、启动DHCP代理、是否使用第三方DHCP服务器、是否可以禁用网卡、代理服务器IP地址、代理服务器端口、显示机器名称、显示正在使用系统名称、显示机器MAC地址、显示机器IP地址、显示提示信息、显示器分辨率宽、显示器分辨率高、显示器刷新频率、缩放比例百分比、开机屏幕复制、开机屏幕扩展、开机恢复图标、开机等待时间、恢复默认设置。路径：分组管理→新建分组→高级。

6 终端管理
点击分组目录中的目标分组，或者点击目标分组的卡片，进入终端管理界面。该界面以列表形式呈现的当前分组的所有终端设备。路径：分组管理→分组目录→目标分组/目标分组卡片。

列表上方有操作工具栏，包含“预设命令”、“添加占位机”、“关机”、“重启”、“唤醒”、“批量操作”、"刷新"、“编辑”按键，右侧有搜索框用于快速查找终端。

列表内展示等主机编号、终端名称、终端IP地址、终端硬件地址、在线状态、终端状态、部署方式、未部署数据、瞬时速度/平均速度（MB/S）、剩余时间（minute）、预设命令、子组编号、中控IP、网卡速度、使用快照等信息以及操作栏。操作栏中有“属性”、“关机”、“重启”、“唤醒”、“删除”等按键。

6.1 设置组内终端预设命令
点击“预设命令”按键，出现预设命令设置表单，可在表单中选择（单选）：更新客户端底层程序、更新客户端底层程序、重新部署信息、设为当前任务盘\盘数、删除当前任务、同步当前程序到系统、完全重新部署、无预置命令。路径：分组管理→选择分组→预设命令。

6.2 添加占位机
点击“添加占位机”按键，出现“是否添加占位机”提示弹窗，点击“确定”，列表会添加一个占位机。路径：分组管理→选择分组→添加占位机。

6.3 全组关机
点击“关机”按键，出现“是否全组关机”提示弹窗，点击“确定”，该分组内所有终端都将关机。路径：分组管理→选择分组→全组关机。

6.4 全组重启
点击“重启”按键，出现“是否全组重启”提示弹窗，点击“确定”，该分组内所有终端都将重启。路径：分组管理→选择分组→全组重启。

6.5 全组唤醒
点击“唤醒”按键，出现“是否全组唤醒”提示弹窗，点击“确定”，该分组内所有终端都将唤醒。路径：分组管理→选择分组→全组唤醒。

6.6 单个终端关机/重启/唤醒/删除
点击目标终端记录操作栏中的“关机”、“重启”、“唤醒”、“删除”按键，对目标终端进行相应的操作。路径：分组管理→选择分组→选择终端→关机/重启/唤醒/删除。

6.7 编辑终端属性
点击目标终端记录操作栏中的“属性”按键，打开属性编辑表单。表单中有设置终端属性
机器MAC地址、机器名称、机器IP地址、机器所属组（点击移动所属分组）、预设命令（单选：更新客户端底层程序、更新客户端底层程序、重新部署信息、设为当前任务盘\盘数、删除当前任务、同步当前程序到系统、完全重新部署、无预置命令）、机器主机号(用来重新分配IP地址)、引用快照、是否强制修改本机网关为组内设置的网关、中控IP。
   
6.8 批量操作
点击“批量操作”按键，每条终端记录前会出现一个复选框，列表上面出现三个按键：“选择单数”、“选择双数”、“全选”，点击这三个按键自动选取相应的终端记录，或者手动点击目标记录的复选框进行选择。此时点击列表上方的“关机”、“重启”、“唤醒”按键时，将对所勾选的终端进行相应的批量操作。路径：分组管理→选择分组→勾选终端→关机/重启/唤醒。

7. 课程管理

7.1 课程管理界面
点击侧边栏"课程管理"，进入课程管理界面。路径：侧边栏→课程管理。

课程管理界面，以表格展示课程表，呈8列布局，第1列为时间，后7列为周一到周日。时间段默认从8:00到18:00，每60分钟一个时间段，课程休息时间10分钟。课程表头部展示 "上一周"按钮、 "下一周"按钮、当前周显示："x年第x周"。

课程内以卡片展示已有课程，蓝色渐变背景表示已安排的课程，橙色渐变背景表示当前选中的课程， 绿色渐变背景表示正在进行的课程（带呼吸动画效果）。卡片内展示课程名称、教室，如卡片内有多个课程，每个课程信息以虚线进行分隔。

课程管理界面上方为“新建课程”、“自定义课程表”、“切换列表”按键。

7.2 新建课程
点击“新建课程”按键，进入新建课程表单，在表单内可以设置课程信息：星期、教室（即分组）、上课时间、上课系统（单选快照）、送单停留时间、课堂描述、教室描述、是否部署到本地磁盘。点击“确定”后新建课程。

7.3 快速新建课程
点击课程表中目标时间段，进入新建课程表单，在表单内可以设置课程信息：星期（自动填入当前选择的时间段）、教室（即分组）、上课时间（自动填入当前选择的时间段）、上课系统（单选快照）、送单停留时间、课堂描述、教室描述、是否部署到本地磁盘。点击“确定”后新建课程。

表单中有“快速创建课程”按键，选择已有课程后，导入已有课程信息。

7.4 编辑课程
点击卡片中的课程名称，进入编辑课程表单。在表单内可以设置课程信息：星期、教室（即分组）、上课时间（单选时间段）、上课系统（单选快照）、送单停留时间、课堂描述、教室描述、是否部署到本地磁盘。

表单中有“删除”按键，点击“删除”按键，出现是否删除该课程提示弹窗，点击“确定”后删除该课程。

7.5自定义课表
点击“自定义课表”按键，打开自定义课表表单，在表单内可以设置：每日总课时(节)、首节课开始时间(分钟)、单节课时长、基础课间休息等必填信息，以及特殊课间休息（第x节课后休息x分钟）、午休时间等非必填信息。点击确定后，课表的第一栏时间会根据计算后的时间段显示课程时间表格。

7.6 切换列表
点击“切换列表”按键，当前课程表视图切换为列表呈现，包含星期、教室（即分组）、上课时间（单选时间段）、上课系统（单选快照）、送单停留时间、课堂描述、教室描述、是否部署到本地磁盘等信息栏以及操作栏。操作栏中有“属性”、“删除”等按键。

点击“属性”按键打开编辑课程表单，点击“删除”按键删除当前课程。

表格上方有一排按键“新建课程”、“切换卡片”，以及一个搜索框。

8. 系统设置
系统设置模块包含服务器端的各项配置和维护功能，是保证海光Desk系统管理版正常运行的重要组成部分。通过合理的系统设置，可以优化系统性能，确保服务稳定性。

8.1 系统设置界面
点击侧边栏"系统设置"，进入系统设置界面。界面采用多卡片布局，分为四个主要配置模块：服务器配置、运行参数配置、服务管理、扩展存储配置。每个模块以独立卡片形式展示，便于分类管理各项系统参数。路径：侧边栏→系统设置。

8.2 运行参数配置
运行参数配置卡片用于设置系统运行的相关参数。实时数据缓存区大小和部署数据缓存区大小默认为200MB，一般不需要改变。在实际部署环境中，如果出现部署异常，可以修改这两个参数值的大小进行调试。

自动选择组部署开关控制部署策略，开启后只部署同一个分组的数据，关闭则可同时部署不同分组。最大并发部署数控制同时进行部署的终端数量。部署超时时间设置部署操作的最大等待时间。

设置完成后点击"应用设置"按键保存配置，点击"恢复默认"按键重置为默认参数。路径：系统设置→运行参数配置→应用设置。

运行参数的合理配置可以优化系统性能，提高部署效率。在调整参数时应根据实际网络环境和硬件配置进行适当调整，避免参数设置不当导致系统性能下降。

9.总结
海光Desk系统管理版操作说明手册详细介绍了系统的各项功能和操作方法。通过规范的操作流程和准确的路径标注，用户可以快速掌握系统的使用方法，充分发挥系统的强大功能。在实际使用过程中，建议用户根据具体需求选择合适的功能组合，并注意定期维护和备份重要数据，确保系统的稳定运行和数据安全。

