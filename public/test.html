<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        h1 {
            color: #409EFF;
            margin-bottom: 20px;
        }
        .status {
            color: #67C23A;
            font-size: 18px;
            margin: 20px 0;
        }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #337ecc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 海光Desk系统管理版</h1>
        <div class="status">✅ 服务器运行正常</div>
        <p>如果你能看到这个页面，说明开发服务器工作正常！</p>
        <button onclick="alert('按钮点击成功！Vue应用即将加载...')">测试按钮</button>
        <br><br>
        <p><strong>访问地址：</strong></p>
        <p>主应用：<a href="/">http://127.0.0.1:8080/</a></p>
        <p>测试页面：<a href="/test.html">http://127.0.0.1:8080/test.html</a></p>
    </div>
</body>
</html>
